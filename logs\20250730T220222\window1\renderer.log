2025-07-30 22:02:23.998 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.vscode-selfhost-test-provider' wants API proposal 'attributableCoverage' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-30 22:02:23.998 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-toolsai.datawrangler' wants API proposal 'debugFocus' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-30 22:02:23.999 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-30 22:02:24.000 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.debugpy' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-30 22:02:24.000 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-azuretools.vscode-azure-github-copilot' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-30 22:02:24.001 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.cpptools' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-30 22:02:24.001 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'vscjava.vscode-java-pack' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-30 22:02:24.001 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'apidev.azure-api-center' wants API proposal 'chatParticipant' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-30 22:02:24.001 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'apidev.azure-api-center' wants API proposal 'languageModels' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-30 22:02:24.024 [warning] Missing property "composerState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.025 [warning] Missing property "mcpServers" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.025 [warning] Missing property "mcpDisabledTools" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.025 [warning] Missing property "availableDefaultModels2" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.025 [warning] Missing property "notepadState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.025 [warning] Missing property "bugbotState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.025 [warning] Missing property "backgroundComposerState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.025 [warning] Missing property "dialogDontAskAgainPreferences" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.026 [warning] Missing property "aiFeaturesCopyPasteState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.026 [warning] Missing property "cursorIgnore" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.026 [warning] Missing property "teamAdminSettings" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.026 [warning] Missing property "shouldShowViewZoneWhenPreviewBoxIsClipped6" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.026 [warning] Missing property "syncDevModeColorTheme" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.026 [warning] Missing property "cppModelsState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.026 [warning] Missing property "isLinterEnabled" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.026 [warning] Missing property "aiSettings" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.027 [warning] Missing property "authenticationSettings" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.027 [warning] Missing property "websiteUrl" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.027 [warning] Missing property "backendUrl" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.027 [warning] Missing property "authClientId" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.027 [warning] Missing property "authDomain" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.027 [warning] Missing property "repoBackendUrl" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.027 [warning] Missing property "contextBankUrl" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.028 [warning] Missing property "telemBackendUrl" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.028 [warning] Missing property "cmdkBackendUrl" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.028 [warning] Missing property "geoCppBackendUrl" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.028 [warning] Missing property "cppConfigBackendUrl" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.028 [warning] Missing property "bcProxyUrl" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.028 [warning] Missing property "credentialsDisplayName" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.028 [warning] Missing property "docState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.029 [warning] Missing property "lastUpdateHiddenTimeInUnixSeconds" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.029 [warning] Missing property "lintRules" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.029 [warning] Missing property "bubbleTimesLeft" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.029 [warning] Missing property "showAgentActionDebugger" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.029 [warning] Missing property "cmdLineHookState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.029 [warning] Missing property "showLinterDebugger" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.029 [warning] Missing property "linterDebuggerState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.030 [warning] Missing property "cacheChatPrompts" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.030 [warning] Missing property "cmdkDiffHistoryEnabled" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.030 [warning] Missing property "shouldOnlyImportOnAccept" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.030 [warning] Missing property "cppAutoImportDecorationStyle" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.030 [warning] Missing property "lintSettings" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.030 [warning] Missing property "lastUpgradeToProNotificationTime" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.031 [warning] Missing property "haveNotSeen" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.031 [warning] Missing property "newUserData" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.031 [warning] Missing property "azureState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.031 [warning] Missing property "interpreterModeSettings" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.031 [warning] Missing property "cppFireOnEveryCursorChange" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.031 [warning] Missing property "personalDocs" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.031 [warning] Missing property "cppInCmdF" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.031 [warning] Missing property "cppManualTriggerWithOpToken" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.031 [warning] Missing property "cppTriggerInComments" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.031 [warning] Missing property "cppShowWhitespaceOnlyChanges" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.032 [warning] Missing property "fastCppEnabled" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.032 [warning] Missing property "cppConfig" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.032 [warning] Missing property "indexRepository" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.032 [warning] Missing property "haveNotImportedFromVSC" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.032 [warning] Missing property "shouldAutoParseCmdKLinks" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.032 [warning] Missing property "aiPreviewsEnabled" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.032 [warning] Missing property "autoCreateNewChatAfterTimeout" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.032 [warning] Missing property "aiPreviewSettings" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.032 [warning] Missing property "chatFadeInAnimationEnabled" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.032 [warning] Missing property "isFileSyncClientEnabled" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.033 [warning] Missing property "membershipType" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.033 [warning] Missing property "useFastApplyModel" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.033 [warning] Missing property "fastApplyModelType" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.033 [warning] Missing property "explicitlyEnableSemanticSearch" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.033 [warning] Missing property "aiCursorHelpEnabled" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.033 [warning] Missing property "showAllCmdKContexts" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.033 [warning] Missing property "backgroundComposerEnv" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.033 [warning] Missing property "markdownTestString" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.033 [warning] Missing property "cppInPeek" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.034 [warning] Missing property "fastSemanticSearchEnabled" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.034 [warning] Missing property "preferredEmbeddingModel" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.034 [warning] Missing property "cursorPredictionUIExperiments" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.034 [warning] Missing property "oneTimeSettings" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.034 [warning] Missing property "indexingState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.034 [warning] Missing property "internalAnalyticsDebugMode" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.034 [warning] Missing property "fullContextOptions" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.034 [warning] Missing property "eligibleForSnippetLearning" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.034 [warning] Missing property "goneThroughCodeSnippetOnboarding" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.034 [warning] Missing property "goneThroughCodeSnippetChangeManagement" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.035 [warning] Missing property "memoriesEnabled" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.035 [warning] Missing property "isRcpServerEnabled" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.035 [warning] Missing property "repositoryIndexingError" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.035 [warning] Missing property "repositoryIndexingStatus" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.035 [warning] Missing property "repositoryLastSyncedTime" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.035 [warning] Missing property "repositoryIndexingJobs" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.035 [warning] Missing property "mainLocalRepositoryProgress" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.035 [warning] Missing property "hasClickedSlackConnect" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.035 [warning] Missing property "shouldHideWarning" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.035 [warning] Missing property "persistentChatMetadata" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.036 [warning] Missing property "aiPanePosition" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.036 [warning] Missing property "shouldRerankByDefault" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.036 [warning] Missing property "indexingData" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.036 [warning] Missing property "composerState" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.036 [warning] Missing property "isCursorPredictionOutOfRangeIndicatorMinimized2" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.036 [warning] Missing property "needsComposerInitialOpening" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.036 [warning] Missing property "approvedProjectMcpServers" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.036 [warning] Missing property "disabledMcpServers" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.036 [warning] Missing property "eligibleForSnippetLearning" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.037 [warning] Missing property "solidJSLeakRecords" in oldValue. Filling with value from initValue. Please add a migration if necessary.
2025-07-30 22:02:24.350 [info] Started local extension host with pid 4312.
2025-07-30 22:02:24.609 [error] Extension 'ms-python.python' appears in product.json but enables LESS API proposals than the extension wants.
package.json (LOSES): contribEditorContentMenu, quickPickSortByLabel, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, codeActionAI, notebookReplDocument, notebookVariableProvider
product.json (WINS): contribEditorContentMenu, quickPickSortByLabel, portsAttributes, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, notebookReplDocument
2025-07-30 22:02:25.582 [error] [Extension Host] (node:4312) ExperimentalWarning: Use `importAttributes` instead of `importAssertions`
(Use `Cursor --trace-warnings ...` to show where the warning was created)
2025-07-30 22:02:27.454 [error] [unauthenticated] Error: ConnectError: [unauthenticated] Error
    at t (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:4246:219342)
    at async Object.cppEditHistoryStatus (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:490:61723)
    at async yHs.pollTelemEnabled (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:6788:22032) [unauthenticated] Error
2025-07-30 22:02:27.540 [error] Error fetching user privacy mode: [unauthenticated] Error
2025-07-30 22:02:27.556 [error] [unauthenticated] Error: ConnectError: [unauthenticated] Error
    at t (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:4246:219342)
    at async Object.availableDocs (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:490:61723)
    at async z7i.availableDocs (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:647:20269)
    at async lGi.z (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:3726:7492) [unauthenticated] Error
2025-07-30 22:02:27.558 [error] [unauthenticated] Error: ConnectError: [unauthenticated] Error
    at t (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:4246:219342)
    at async Object.getDefaultModelNudgeData (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:490:61723)
    at async z7i.performDefaultModelRequest (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:647:22119) [unauthenticated] Error
2025-07-30 22:02:27.565 [error] [unauthenticated] Error: ConnectError: [unauthenticated] Error
    at t (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:4246:219342)
    at async Object.getUserPrivacyMode (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:490:61723)
    at async T8i.fetchUserPrivacyMode (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:492:40324)
    at async T8i.refreshPrivacyMode (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:492:40064) [unauthenticated] Error
2025-07-30 22:02:27.571 [error] [unauthenticated] Error: ConnectError: [unauthenticated] Error
    at t (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:4246:219342)
    at async Object.availableModels (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:490:61723)
    at async fHs.keepCppModelStateUpdated (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:6761:11950)
    at async fHs.loadCppConfigIncludingHandlingProAccess (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:6778:17224) [unauthenticated] Error
2025-07-30 22:02:27.571 [error] [unauthenticated] Error: ConnectError: [unauthenticated] Error
    at t (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:4246:219342)
    at async Object.availableDocs (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:490:61723)
    at async z7i.availableDocs (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:647:20269)
    at async Object.fn (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:6799:49554) [unauthenticated] Error
2025-07-30 22:02:27.714 [info] [perf] Render performance baseline is 18ms
