2025-07-30 22:02:25.585 [info] Extension host with pid 4312 started
2025-07-30 22:02:25.594 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-30 22:02:25.611 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-07-30 22:02:25.624 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-07-30 22:02:25.674 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-07-30 22:02:25.934 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-30 22:02:25.939 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-30 22:02:25.987 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-30 22:02:26.076 [info] Eager extensions activated
2025-07-30 22:02:26.082 [info] ExtensionService#_doActivateExtension anysphere.cursor-always-local, startup: false, activationEvent: 'onStartupFinished'
2025-07-30 22:02:26.240 [info] ExtensionService#_doActivateExtension anysphere.cursor-deeplink, startup: false, activationEvent: 'onStartupFinished'
2025-07-30 22:02:26.282 [info] ExtensionService#_doActivateExtension anysphere.cursor-retrieval, startup: false, activationEvent: 'onStartupFinished'
2025-07-30 22:02:26.457 [info] ExtensionService#_doActivateExtension anysphere.cursor-shadow-workspace, startup: false, activationEvent: 'onStartupFinished'
2025-07-30 22:02:26.514 [info] ExtensionService#_doActivateExtension anysphere.cursor-tokenize, startup: false, activationEvent: 'onStartupFinished'
2025-07-30 22:02:26.653 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-30 22:02:26.658 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-30 22:02:37.964 [info] Extension host terminating: renderer closed the MessagePort
2025-07-30 22:02:37.980 [error] Error: Channel has been closed
	at n (file:///c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:141:2439)
	at Object.appendLine (file:///c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:141:2578)
	at c (c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\cursor-always-local\dist\main.js:2:1732910)
	at t.deactivate (c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\cursor-always-local\dist\main.js:2:1734566)
	at c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\cursor-always-local\dist\main.js:2:310850
	at t.deactivate (c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\extensions\cursor-always-local\dist\main.js:2:310909)
	at async Promise.all (index 1)
	at async zpe.$ (file:///c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:139:10212)
2025-07-30 22:02:37.981 [info] Extension host with pid 4312 exiting with code 0
