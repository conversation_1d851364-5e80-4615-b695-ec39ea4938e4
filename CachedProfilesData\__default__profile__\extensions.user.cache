{"input": {"location": {"$mid": 1, "external": "vscode-userdata:/c%3A/Users/<USER>/.cursor/extensions/extensions.json", "path": "/c:/Users/<USER>/.cursor/extensions/extensions.json", "scheme": "vscode-userdata"}, "mtime": 1750448480228, "profile": true, "profileScanOptions": {"bailOutWhenFileNotFound": true}, "type": 1, "excludeObsolete": true, "validate": true, "productVersion": "1.96.2", "productDate": "2025-06-15T06:55:04.603Z", "productCommit": "979ba33804ac150108481c14e0b5cb970bda3260", "devMode": false, "language": "en", "translations": {}}, "result": [{"type": 1, "identifier": {"id": "ms-python.vscode-pylance", "uuid": "364d2426-116a-433a-a5d8-a5098dc3afbd"}, "manifest": {"name": "vscode-pylance", "displayName": "<PERSON><PERSON><PERSON>", "description": "A performant, feature-rich language server for Python in VS Code", "version": "2024.8.1", "license": "SEE LICENSE IN LICENSE.txt", "author": {"name": "Microsoft Corporation"}, "publisher": "ms-python", "private": true, "bugs": {"url": "https://github.com/microsoft/pylance-release/issues"}, "repository": {"type": "git", "url": "https://github.com/microsoft/pylance-release"}, "engines": {"vscode": "^1.89.0"}, "keywords": ["python"], "categories": ["Programming Languages"], "icon": "images/icon.png", "main": "./dist/extension.bundle.js", "browser": "./dist/browser.extension.bundle.js", "activationEvents": ["onLanguage:python", "workspaceContains:pyrightconfig.json"], "extensionDependencies": ["ms-python.python"], "capabilities": {"untrustedWorkspaces": {"supported": "limited", "description": "Python will not be executed; the latest Python version will be used for analysis and installed libraries will not be available.", "restrictedConfigurations": ["python.analysis.nodeExecutable"]}, "virtualWorkspaces": {"supported": "limited", "description": "Analysis is limited to opened files."}}, "contributes": {"commands": [{"command": "pylance.reportIssue", "title": "Pylance: Report Issue..."}, {"command": "pylance.indexing.clearPersistedIndices", "title": "Pylance: Clear All Persisted Indices"}, {"command": "pylance.profiling.start", "title": "Pylance: Start Profiling"}, {"command": "pylance.profiling.stop", "title": "Pylance: Stop Profiling"}, {"command": "pylance.logging.start", "title": "Pylance: Start Logging"}, {"command": "pylance.logging.stop", "title": "Pylance: Stop Logging"}, {"command": "pylance.gotoOutputChannel", "title": "Pylance: Go to output channel"}, {"command": "pylance.dumpTokens", "title": "Dump token streams ...", "category": "Pylance debugging", "enablement": "editorLangId == python && config.pyright.development"}, {"command": "pylance.dumpNodes", "title": "Dump parse tree ...", "category": "Pylance debugging", "enablement": "editorLangId == python && config.pyright.development"}, {"command": "pylance.dumpTypes", "title": "Dump type info ...", "category": "Pylance debugging", "enablement": "editorLangId == python && config.pyright.development"}, {"command": "pylance.dumpCachedTypes", "title": "Pylance: Dump cached type info ...", "category": "Pylance debugging", "enablement": "editorLangId == python && config.pyright.development"}, {"command": "pylance.dumpCodeFlowGraph", "title": "Pylance: Dump code flow graph for node ...", "category": "Pylance debugging", "enablement": "editorLangId == python && config.pyright.development"}, {"command": "pylance.learnMoreAboutImportResolution", "title": "Learn more about import resolution", "enablement": "false"}], "menus": {"commandPalette": [{"command": "pylance.reportIssue"}, {"command": "pylance.indexing.clearPersistedIndices", "when": "!isWeb"}, {"command": "pylance.profiling.start", "when": "!isWeb"}, {"command": "pylance.profiling.stop", "when": "!isWeb"}, {"command": "pylance.logging.start", "when": "!isWeb"}, {"command": "pylance.logging.stop", "when": "!isWeb"}, {"command": "pylance.dumpTokens", "when": "editorLangId == python && config.pyright.development"}, {"command": "pylance.dumpNodes", "when": "editorLangId == python && config.pyright.development"}, {"command": "pylance.dumpTypes", "when": "editorLangId == python && config.pyright.development"}, {"command": "pylance.dumpCachedTypes", "when": "editorLangId == python && config.pyright.development"}, {"command": "pylance.dumpCodeFlowGraph", "when": "editorLangId == python && config.pyright.development"}]}, "configurationDefaults": {"[python]": {"editor.formatOnType": true, "editor.wordBasedSuggestions": "off"}}, "configuration": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "properties": {"python.analysis.inlayHints.variableTypes": {"type": "boolean", "default": false, "markdownDescription": "Enable/disable inlay hints for variable types. Hints are not displayed for assignments of literals or constants:\n```python\nfoo':list[str]' = [\"a\"]\n \n```\n", "scope": "resource"}, "python.analysis.inlayHints.functionReturnTypes": {"type": "boolean", "default": false, "markdownDescription": "Enable/disable inlay hints for function return types:\n```python\ndef foo(x:int) '-> int':\n\treturn x\n```\n", "scope": "resource"}, "python.analysis.inlayHints.callArgumentNames": {"type": "string", "default": "off", "markdownDescription": "Enable/disable inlay hints for call argument names:\n```python\ndatetime('year='2019, 'month='10, 'day='27)\n```\n", "enum": ["off", "partial", "all"], "enumDescriptions": ["Disable inlay hints for call argument names.", "Enable inlay hints for positional-or-keyword arguments while ignoring positional-only and keyword-only.", "Enable inlay hints for positional-or-keyword and positional-only arguments while ignoring keyword-only."], "scope": "resource"}, "python.analysis.inlayHints.pytestParameters": {"type": "boolean", "default": false, "markdownDescription": "Enable/disable inlay hints for pytest function parameter types:\n```python\ndef test_foo(my_fixture: 'str'):\n\tassert(my_fixture == 'foo')\n```\n", "scope": "resource"}, "python.analysis.completeFunctionParens": {"type": "boolean", "default": false, "description": "Add parentheses to function completions.", "scope": "resource"}, "python.analysis.autoImportCompletions": {"type": "boolean", "default": false, "description": "Enable auto-import completions.", "scope": "resource"}, "python.analysis.autoFormatStrings": {"type": "boolean", "default": false, "description": "When typing a '{' in a string, automatically prefix the string with an 'f'.", "scope": "window"}, "python.analysis.autoSearchPaths": {"type": "boolean", "default": true, "description": "Automatically add common search paths like 'src'.", "scope": "resource"}, "python.analysis.stubPath": {"type": "string", "default": "typings", "description": "Path to directory containing custom type stub files.", "scope": "resource"}, "python.analysis.diagnosticMode": {"type": "string", "default": "openFilesOnly", "description": "Analysis scope for showing diagnostics.", "enum": ["openFilesOnly", "workspace"], "enumDescriptions": ["Analyzes and reports errors on only open files.", "Analyzes and reports errors on all files in the workspace."], "scope": "resource"}, "python.analysis.extraPaths": {"type": "array", "default": [], "items": {"type": "string"}, "description": "Additional import search resolution paths", "scope": "resource"}, "python.analysis.include": {"type": "array", "default": [], "items": {"type": "string"}, "description": "Paths of directories or files that should be included. If no paths are specified, <PERSON><PERSON><PERSON> defaults to the workspace root directory. Paths may contain wildcard characters ** (a directory or multiple levels of directories), * (a sequence of zero or more characters), or ? (a single character). If the path contains a '${workspaceFolder:<workspaceName>}' variable, the glob pattern will only apply to the corresponding workspace in a multi-root workspace environment.", "scope": "resource"}, "python.analysis.exclude": {"type": "array", "default": [], "items": {"type": "string"}, "description": "Paths of directories or files that should not be included. These override the include directories, allowing specific subdirectories to be excluded. Note that files in the exclude paths may still be included in the analysis if they are referenced (imported) by source files that are not excluded. Paths may contain wildcard characters ** (a directory or multiple levels of directories), * (a sequence of zero or more characters), or ? (a single character). If no exclude paths are specified, <PERSON><PERSON><PERSON> automatically excludes the following: `**/node_modules`, `**/__pycache__`, `.git` and any virtual environment directories. If the path contains a '${workspaceFolder:<workspaceName>}' variable, the glob pattern will only apply to the corresponding workspace in a multi-root workspace environment.", "scope": "resource"}, "python.analysis.ignore": {"type": "array", "default": [], "items": {"type": "string"}, "description": "Paths of directories or files whose diagnostic output (errors and warnings) should be suppressed even if they are an included file or within the transitive closure of an included file. Paths may contain wildcard characters ** (a directory or multiple levels of directories), * (a sequence of zero or more characters), or ? (a single character). If the path contains a '${workspaceFolder:<workspaceName>}' variable, the glob pattern will only apply to the corresponding workspace in a multi-root workspace environment.", "scope": "resource"}, "python.analysis.indexing": {"type": "boolean", "default": true, "description": "Enable indexing of installed third party libraries and user files for language features such as auto-import, add import, workspace symbols and etc.", "scope": "resource"}, "python.analysis.userFileIndexingLimit": {"type": "number", "default": 2000, "description": "Maximum number of user files to index in the workspace. Indexing files is a performance-intensive task. Please use this setting to limit the number of files you want us to index. If you enter -1, we will index all files.", "scope": "resource"}, "python.analysis.persistAllIndices": {"type": "boolean", "default": true, "description": "Indices for all third party libraries will be persisted to disk.", "scope": "resource"}, "python.analysis.extraCommitChars": {"type": "boolean", "default": false, "description": "Allow using '.', '(' as commit characters when applicable.", "scope": "resource"}, "python.analysis.useLibraryCodeForTypes": {"type": "boolean", "default": true, "description": "Use library implementations to extract type information when type stub is not present.", "scope": "resource"}, "python.analysis.autoIndent": {"type": "boolean", "default": true, "description": "Automatically adjust indentation based on language semantics when typing Python code.", "scope": "window"}, "python.analysis.typeCheckingMode": {"type": "string", "default": "off", "enum": ["off", "basic", "standard", "strict"], "description": "Defines the default rule set for type checking.", "enumDescriptions": ["Show diagnostics for invalid syntax, unresolved imports, undefined variables.", "All \"off\" rules + basic type checking rules.", "All \"off\" rules + basic type checking rules + standard typechecker rules.", "All \"off\" rules + all type checking rules."], "scope": "resource", "tags": ["experimental"]}, "python.analysis.fixAll": {"type": "array", "default": [], "items": {"enum": ["source.unusedImports", "source.convertImportFormat"], "enumDescriptions": ["Remove unused imports.", "Convert import format following python.analysis.importFormat."]}, "description": "source fixes to run in fix all.", "scope": "resource"}, "python.analysis.packageIndexDepths": {"type": "array", "default": [{"name": "sklearn", "depth": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "depth": 2}, {"name": "scipy", "depth": 2}, {"name": "django", "depth": 2}, {"name": "flask", "depth": 2}, {"name": "<PERSON><PERSON><PERSON>", "depth": 2}], "items": {"type": "object", "label": "Per package settings", "required": ["name"], "properties": {"name": {"type": "string", "label": "Package name to configure. Empty name means all packages.", "default": ""}, "depth": {"type": "integer", "label": "Depth to scan modules.", "default": 1}, "includeAllSymbols": {"type": "boolean", "label": "'true' means include all symbols. 'false' means only symbols in __all__.", "default": false}}}, "markdownDescription": "Used to override how many levels under installed packages to index on a per package basis. By default, only top-level modules are indexed (depth = 1). To index submodules, increase depth by 1 for each level of submodule you want to index. Accepted values are:\n```JSON\n{\"name\": \"package name (str)\",\n \"depth\": \"depth to scan (int)\",\n \"includeAllSymbols\": \"whether to include all symbols (bool)\"}\n\n```\nIf `include all symbols` is set to `false`, only symbols in each package's `__all__` are included. When it's set to `true`, <PERSON><PERSON><PERSON> will index every module/top level symbol declarations in the file.  \n  \nUsage example: \n```JSON\n[\n\t{\"name\": \"sklearn\", \"depth\": 2, \"includeAllSymbols\": true},\n\t{\"name\": \"matplotlib\", \"depth\": 3, \"includeAllSymbols\": false}\n]\n\n```\n", "scope": "resource"}, "python.analysis.diagnosticSeverityOverrides": {"type": "object", "markdownDescription": "Allows a user to override the severity levels for individual diagnostics. Use the rule name as a key and one of \"error\", \"warning\", \"information\", \"none\", `true` (alias for \"error\") or `false` (alias for \"none\") as value. The default value shown for each diagnostic is the default when \"python.analysis.typeCheckingMode\" is set to \"off\". See [here](https://github.com/microsoft/pyright/blob/main/docs/configuration.md#diagnostic-rule-defaults) for defaults for each type checking mode (\"off\", \"basic\" and \"strict\").", "scope": "resource", "properties": {"reportGeneralTypeIssues": {"type": ["string", "boolean"], "description": "Diagnostics for general type inconsistencies, unsupported operations, argument/parameter mismatches, etc. Covers all of the basic type-checking rules not covered by other rules. Does not include syntax errors.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportPropertyTypeMismatch": {"type": ["string", "boolean"], "description": "Diagnostics for property whose setter and getter have mismatched types.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportFunctionMemberAccess": {"type": ["string", "boolean"], "description": "Diagnostics for member accesses on functions.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportMissingImports": {"type": ["string", "boolean"], "description": "Diagnostics for imports that have no corresponding imported python file or type stub file.", "default": "warning", "enum": ["none", "information", "warning", "error", true, false]}, "reportMissingModuleSource": {"type": ["string", "boolean"], "description": "Diagnostics for imports that have no corresponding source file. This happens when a type stub is found, but the module source file was not found, indicating that the code may fail at runtime when using this execution environment. Type checking will be done using the type stub.", "default": "warning", "enum": ["none", "information", "warning", "error", true, false]}, "reportInvalidTypeForm": {"type": ["string", "boolean"], "description": "Diagnostics for type expression that uses an invalid form.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportMissingTypeStubs": {"type": ["string", "boolean"], "description": "Diagnostics for imports that have no corresponding type stub file (either a typeshed file or a custom type stub). The type checker requires type stubs to do its best job at analysis.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportImportCycles": {"type": ["string", "boolean"], "description": "Diagnostics for cyclical import chains. These are not errors in Python, but they do slow down type analysis and often hint at architectural layering issues. Generally, they should be avoided.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnusedImport": {"type": ["string", "boolean"], "description": "Diagnostics for an imported symbol that is not referenced within that file.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnusedClass": {"type": ["string", "boolean"], "description": "Diagnostics for a class with a private name (starting with an underscore) that is not accessed.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnusedFunction": {"type": ["string", "boolean"], "description": "Diagnostics for a function or method with a private name (starting with an underscore) that is not accessed.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnusedVariable": {"type": ["string", "boolean"], "description": "Diagnostics for a variable that is not accessed.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportDuplicateImport": {"type": ["string", "boolean"], "description": "Diagnostics for an imported symbol or module that is imported more than once.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportWildcardImportFromLibrary": {"type": ["string", "boolean"], "description": "Diagnostics for an wildcard import from an external library.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportAbstractUsage": {"type": ["string", "boolean"], "description": "Diagnostics for an attempt to instantiate an abstract or protocol class or use an abstract method.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportArgumentType": {"type": ["string", "boolean"], "description": "Diagnostics for a type incompatibility for an argument to a call.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportAssertTypeFailure": {"type": ["string", "boolean"], "description": "Diagnostics for a type incompatibility detected by a typing.assert_type call.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportAssignmentType": {"type": ["string", "boolean"], "description": "Diagnostics for type incompatibilities for assignments.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportAttributeAccessIssue": {"type": ["string", "boolean"], "description": "Diagnostics for issues involving attribute accesses.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportCallIssue": {"type": ["string", "boolean"], "description": "Diagnostics for issues involving call expressions and arguments.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportInconsistentOverload": {"type": ["string", "boolean"], "description": "Diagnostics for inconsistencies between function overload signatures and implementation.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportIndexIssue": {"type": ["string", "boolean"], "description": "Diagnostics related to index operations and expressions.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportInvalidTypeArguments": {"type": ["string", "boolean"], "description": "Diagnostics for invalid type argument usage.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportNoOverloadImplementation": {"type": ["string", "boolean"], "description": "Diagnostics for an overloaded function or method with a missing implementation.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportOperatorIssue": {"type": ["string", "boolean"], "description": "Diagnostics for related to unary or binary operators.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportOptionalSubscript": {"type": ["string", "boolean"], "description": "Diagnostics for an attempt to subscript (index) a variable with an Optional type.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportOptionalMemberAccess": {"type": ["string", "boolean"], "description": "Diagnostics for an attempt to access a member of a variable with an Optional type.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportOptionalCall": {"type": ["string", "boolean"], "description": "Diagnostics for an attempt to call a variable with an Optional type.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportOptionalIterable": {"type": ["string", "boolean"], "description": "Diagnostics for an attempt to use an Optional type as an iterable value (e.g. within a for statement).", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportOptionalContextManager": {"type": ["string", "boolean"], "description": "Diagnostics for an attempt to use an Optional type as a context manager (as a parameter to a with statement).", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportOptionalOperand": {"type": ["string", "boolean"], "description": "Diagnostics for an attempt to use an Optional type as an operand to a binary or unary operator (like '+', '==', 'or', 'not').", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportRedeclaration": {"type": ["string", "boolean"], "description": "Diagnostics for an attempt to declare the type of a symbol multiple times.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportReturnType": {"type": ["string", "boolean"], "description": "Diagnostics related to function return type compatibility.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportTypedDictNotRequiredAccess": {"type": ["string", "boolean"], "description": "Diagnostics for an attempt to access a non-required key within a TypedDict without a check for its presence.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUntypedFunctionDecorator": {"type": ["string", "boolean"], "description": "Diagnostics for function decorators that have no type annotations. These obscure the function type, defeating many type analysis features.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUntypedClassDecorator": {"type": ["string", "boolean"], "description": "Diagnostics for class decorators that have no type annotations. These obscure the class type, defeating many type analysis features.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUntypedBaseClass": {"type": ["string", "boolean"], "description": "Diagnostics for base classes whose type cannot be determined statically. These obscure the class type, defeating many type analysis features.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUntypedNamedTuple": {"type": ["string", "boolean"], "description": "Diagnostics when “namedtuple” is used rather than “NamedTuple”. The former contains no type information, whereas the latter does.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportPrivateUsage": {"type": ["string", "boolean"], "description": "Diagnostics for incorrect usage of private or protected variables or functions. Protected class members begin with a single underscore _ and can be accessed only by subclasses. Private class members begin with a double underscore but do not end in a double underscore and can be accessed only within the declaring class. Variables and functions declared outside of a class are considered private if their names start with either a single or double underscore, and they cannot be accessed outside of the declaring module.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportTypeCommentUsage": {"type": ["string", "boolean"], "description": "Diagnostics for usage of deprecated type comments.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportPrivateImportUsage": {"type": ["string", "boolean"], "description": "Diagnostics for incorrect usage of symbol imported from a \"py.typed\" module that is not re-exported from that module.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportConstantRedefinition": {"type": ["string", "boolean"], "description": "Diagnostics for attempts to redefine variables whose names are all-caps with underscores and numerals.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportDeprecated": {"type": ["string", "boolean"], "description": "Diagnostics for use of deprecated classes or functions.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportIncompatibleMethodOverride": {"type": ["string", "boolean"], "description": "Diagnostics for methods that override a method of the same name in a base class in an incompatible manner (wrong number of parameters, incompatible parameter types, or incompatible return type).", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportIncompatibleVariableOverride": {"type": ["string", "boolean"], "description": "Diagnostics for overrides in subclasses that redefine a variable in an incompatible way.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportInconsistentConstructor": {"type": ["string", "boolean"], "description": "Diagnostics for __init__ and __new__ methods whose signatures are inconsistent.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportOverlappingOverload": {"type": ["string", "boolean"], "description": "Diagnostics for function overloads that overlap in signature and obscure each other or have incompatible return types.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportPossiblyUnboundVariable": {"type": ["string", "boolean"], "description": "Diagnostics for the use of variables that may be unbound on some code paths.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportMissingSuperCall": {"type": ["string", "boolean"], "description": "Diagnostics for missing call to parent class for inherited `__init__` methods.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUninitializedInstanceVariable": {"type": ["string", "boolean"], "description": "Diagnostics for instance variables that are not declared or initialized within class body or `__init__` method.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportInvalidStringEscapeSequence": {"type": ["string", "boolean"], "description": "Diagnostics for invalid escape sequences used within string literals. The Python specification indicates that such sequences will generate a syntax error in future versions.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnknownParameterType": {"type": ["string", "boolean"], "description": "Diagnostics for input or return parameters for functions or methods that have an unknown type.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnknownArgumentType": {"type": ["string", "boolean"], "description": "Diagnostics for call arguments for functions or methods that have an unknown type.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnknownLambdaType": {"type": ["string", "boolean"], "description": "Diagnostics for input or return parameters for lambdas that have an unknown type.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnknownVariableType": {"type": ["string", "boolean"], "description": "Diagnostics for variables that have an unknown type..", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnknownMemberType": {"type": ["string", "boolean"], "description": "Diagnostics for class or instance variables that have an unknown type.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportMissingParameterType": {"type": ["string", "boolean"], "description": "Diagnostics for parameters that are missing a type annotation.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportMissingTypeArgument": {"type": ["string", "boolean"], "description": "Diagnostics for generic class reference with missing type arguments.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportInvalidTypeVarUse": {"type": ["string", "boolean"], "description": "Diagnostics for improper use of type variables in a function signature.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportCallInDefaultInitializer": {"type": ["string", "boolean"], "description": "Diagnostics for function calls within a default value initialization expression. Such calls can mask expensive operations that are performed at module initialization time.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnnecessaryIsInstance": {"type": ["string", "boolean"], "description": "Diagnostics for 'isinstance' or 'issubclass' calls where the result is statically determined to be always true. Such calls are often indicative of a programming error.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnnecessaryCast": {"type": ["string", "boolean"], "description": "Diagnostics for 'cast' calls that are statically determined to be unnecessary. Such calls are sometimes indicative of a programming error.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnnecessaryComparison": {"type": ["string", "boolean"], "description": "Diagnostics for '==' and '!=' comparisons that are statically determined to be unnecessary. Such calls are sometimes indicative of a programming error.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnnecessaryContains": {"type": ["string", "boolean"], "description": "Diagnostics for 'in' operation that is statically determined to be unnecessary. Such operations are sometimes indicative of a programming error.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportAssertAlwaysTrue": {"type": ["string", "boolean"], "description": "Diagnostics for 'assert' statement that will provably always assert. This can be indicative of a programming error.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportSelfClsParameterName": {"type": ["string", "boolean"], "description": "Diagnostics for a missing or misnamed “self” parameter in instance methods and “cls” parameter in class methods. Instance methods in metaclasses (classes that derive from “type”) are allowed to use “cls” for instance methods.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportImplicitStringConcatenation": {"type": ["string", "boolean"], "description": "Diagnostics for two or more string literals that follow each other, indicating an implicit concatenation. This is considered a bad practice and often masks bugs such as missing commas.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportInvalidStubStatement": {"type": ["string", "boolean"], "description": "Diagnostics for type stub statements that do not conform to PEP 484.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportIncompleteStub": {"type": ["string", "boolean"], "description": "Diagnostics for the use of a module-level “__getattr__” function, indicating that the stub is incomplete.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUndefinedVariable": {"type": ["string", "boolean"], "description": "Diagnostics for undefined variables.", "default": "warning", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnboundVariable": {"type": ["string", "boolean"], "description": "Diagnostics for the use of unbound variables.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnhashable": {"type": ["string", "boolean"], "description": "Diagnostics for the use of an unhashable object in a container that requires hashability.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnusedCallResult": {"type": ["string", "boolean"], "description": "Diagnostics for call expressions whose results are not consumed and are not None.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnusedCoroutine": {"type": ["string", "boolean"], "description": "Diagnostics for call expressions that return a Coroutine and whose results are not consumed.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnusedExcept": {"type": ["string", "boolean"], "description": "Diagnostics for unreachable except clause.", "default": "error", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnusedExpression": {"type": ["string", "boolean"], "description": "Diagnostics for simple expressions whose value is not used in any way.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnsupportedDunderAll": {"type": ["string", "boolean"], "description": "Diagnostics for unsupported operations performed on __all__.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportUnnecessaryTypeIgnoreComment": {"type": ["string", "boolean"], "description": "Diagnostics for '# type: ignore' comments that have no effect.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportMatchNotExhaustive": {"type": ["string", "boolean"], "description": "Diagnostics for 'match' statements that do not exhaustively match all possible values.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportShadowedImports": {"type": ["string", "boolean"], "description": "Diagnostics for files that are overriding a module in the stdlib.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}, "reportImplicitOverride": {"type": ["string", "boolean"], "description": "Diagnostics for overridden methods that do not include an `@override` decorator.", "default": "none", "enum": ["none", "information", "warning", "error", true, false]}}}, "python.analysis.disableTaggedHints": {"type": "boolean", "default": false, "scope": "resource", "description": "Disable hint diagnostics with special hints for grayed-out or strike-through text."}, "python.analysis.logLevel": {"type": "string", "default": "Information", "description": "Specifies the level of logging for the Output panel", "enum": ["Error", "Warning", "Information", "Trace"], "scope": "window"}, "python.analysis.typeshedPaths": {"type": "array", "default": [], "items": {"type": "string"}, "description": "Paths to look for typeshed modules.", "scope": "resource"}, "python.analysis.importFormat": {"type": "string", "default": "absolute", "enum": ["absolute", "relative"], "description": "Defines the default format for import module.", "enumDescriptions": ["Use absolute import format when creating new import statement.", "Use relative import format when creating new import statement."], "scope": "resource"}, "python.analysis.enableSyncServer": {"type": "boolean", "default": false, "scope": "window", "description": "Use sync server mode [Experimental]. For internal use only, may cause <PERSON><PERSON><PERSON> to stop working.", "tags": ["experimental"]}, "python.analysis.experimentalserver": {"type": "boolean", "default": false, "scope": "window", "description": "testing experiemental server", "tags": ["experimental"]}, "python.analysis.reportExtraTelemetry": {"type": "boolean", "default": false, "scope": "window", "description": "Report extra telemetry [Experimental]. For internal use only, may cause <PERSON><PERSON><PERSON> to slow down.", "tags": ["experimental"]}, "python.analysis.enablePytestSupport": {"type": "boolean", "default": true, "description": "Enables pytest support in Pylance.", "scope": "resource"}, "python.analysis.gotoDefinitionInStringLiteral": {"type": "boolean", "default": true, "description": "For string literals that look like module names, enables go to definition to go to the module.", "scope": "resource"}, "python.analysis.nodeExecutable": {"type": "string", "default": "", "markdownDescription": "Path to a Node.js executable to use for running the language server. If not specified, the language server will use the Node.js executable that ships with VS Code. Set this option if you're having trouble with <PERSON><PERSON><PERSON> running out of memory. See [here](https://aka.ms/AApf7ox) for more information.", "scope": "machine"}, "python.analysis.supportRestructuredText": {"type": "boolean", "default": false, "markdownDescription": "Enable/disable support for reStructuredText in docstrings. Experimental, may cause docstrings to no longer render.", "scope": "window", "tags": ["experimental"]}, "python.analysis.cacheLSPData": {"type": "boolean", "default": false, "description": "Cache LSP data for faster completions. Experimental, may cause <PERSON><PERSON><PERSON> to stop working.", "scope": "machine", "tags": ["experimental"]}, "python.analysis.regenerateStdLibIndices": {"type": "boolean", "default": false, "markdownDescription": "Instead of relying on the shared stdlib.json indices for all Python versions, generate unique indices tailored to each workspace's specific Python version and platform. This regeneration process will affect performance, unlike using the prebuilt stdlib indices.", "scope": "resource"}}}, "jsonValidation": [{"fileMatch": "pyrightconfig.json", "url": "./dist/schemas/pyrightconfig.schema.json"}], "semanticTokenTypes": [{"id": "module", "description": "module", "superType": "namespace"}, {"id": "intrinsic", "description": "intrinsic", "superType": "operator"}, {"id": "selfParameter", "description": "self parameter", "superType": "parameter"}, {"id": "clsParameter", "description": "cls parameter", "superType": "parameter"}, {"id": "magicFunction", "description": "magic aka dunder function", "superType": "function"}, {"id": "builtinConstant", "description": "constants like True, False, None, or __debug__", "superType": "constant"}, {"id": "parenthesis", "description": "() parenthesis"}, {"id": "bracket", "description": "[] bracket"}, {"id": "<PERSON><PERSON><PERSON>", "description": "{} curly brace"}, {"id": "colon", "description": "colon token"}, {"id": "semicolon", "description": "semicolon token"}, {"id": "arrow", "description": "arrow token"}], "semanticTokenModifiers": [{"id": "typeHint", "description": "inside a type annotation"}, {"id": "typeHintComment", "description": "inside a comment style type annotation"}, {"id": "decorator", "description": "inside a decorator"}, {"id": "builtin", "description": "built-in identifier"}, {"id": "overridden", "description": "overridden token"}], "semanticTokenScopes": [{"language": "python", "scopes": {"selfParameter": ["variable.parameter.function.language.special.self.python"], "clsParameter": ["variable.parameter.function.language.special.cls.python"], "magicFunction": ["support.function.magic.python"], "*.typeHintComment": ["comment.typehint.type.notation.python"], "*.overridden": ["support.function.magic.python"], "function.decorator": ["meta.function.decorator.python"], "class.decorator": ["meta.function.decorator.python"], "builtinConstant": ["constant.language.python"], "parenthesis": ["source.python"], "bracket": ["source.python"], "curlybrace": ["source.python"], "colon": ["punctuation.separator.colon.python"], "semicolon": ["source.python"], "arrow": ["punctuation.separator.annotation.result.python"]}}]}, "scripts": {"clean": "shx rm -rf ./dist ./out NOTICE.txt", "package": "vsce package --githubBranch main", "vscode:prepublish": "npm run clean && shx cp ../../NOTICE.txt . && node --max_old_space_size=8192 ./node_modules/webpack-cli/bin/cli.js --mode production --progress", "webpack": "node --max_old_space_size=8192 ./node_modules/webpack-cli/bin/cli.js --mode development --progress", "webpack-dev": "npm run clean && node --max_old_space_size=8192 ./node_modules/webpack-cli/bin/cli.js --mode development --watch --progress", "webpack-dev-fast": "npm run clean && webpack --env fast --mode development --watch --progress", "build:tests": "tsc", "watch:tests": "tsc --watch", "encryptText": "ts-node --project ./tsconfig.json -r tsconfig-paths/register ./src/encryptText.ts", "test": "npm run webpack && npm run test:extension && npm run test:web", "test:extension": "npm run build:tests && node ./src/tests/runTests.js extension", "test:smoke": "npm run build:tests && node ./src/tests/runTests.js smoke", "test:web": "npm run webpack && node ./build/launchWebTests.js", "test:copytestbundle": "shx cp ./dist/browser.tests/web/index.bundle.js ./test-web/ms-python.vscode-pylance/dist/browser.tests/web/index.bundle.js", "test:web:exists": "vscode-test-web --coi --extensionDevelopmentPath=./test-web --extensionTestsPath=./test-web/ms-python.vscode-pylance/dist/browser.tests/web/index.bundle.js --verbose", "test:web:exists:nocoi": "vscode-test-web --extensionDevelopmentPath=./test-web --extensionTestsPath=./test-web/ms-python.vscode-pylance/dist/browser.tests/web/index.bundle.js --verbose"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/mocha": "^10.0.6", "@types/node": "^17.0.45", "@types/source-map-support": "^0.5.10", "@types/vscode": "^1.82.0", "@types/webpack": "^5.28.5", "@types/webpack-env": "^1.18.4", "@types/which": "^3.0.3", "@vscode/test-electron": "^2.3.9", "@vscode/test-web": "^0.0.51", "copy-webpack-plugin": "^11.0.0", "esbuild-loader": "^3.2.0", "javascript-obfuscator": "4.1.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-junit": "^16.0.0", "mocha": "^10.3.0", "shx": "^0.3.4", "source-map-support": "^0.5.21", "terser-webpack-plugin": "^5.3.10", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "ts-mockito": "^2.6.1", "ts-node": "^10.9.2", "typescript": "~5.2", "vsce": "^2.7.0", "webpack": "^5.90.1", "webpack-cli": "^5.1.4", "webpack-env": "^0.8.0", "webpack-obfuscator": "^3.5.1"}, "dependencies": {"@vscode/extension-telemetry": "^0.9.2", "@vscode/sync-api-common": "^0.9.0", "@vscode/sync-api-service": "^0.9.0", "buffer": "^6.0.3", "events": "^3.3.0", "path-browserify": "^1.0.1", "semver": "^7.6.0", "stream-browserify": "^3.0.0", "typescript-char": "^0.0.0", "vscode-languageclient": "^10.0.0-next.2", "vscode-languageserver": "^10.0.0-next.2", "vscode-languageserver-protocol": "^3.17.6-next.3", "vscode-languageserver-types": "^3.17.6-next.3", "vscode-tas-client": "^0.1.84", "which": "^4.0.0"}}, "location": {"$mid": 1, "path": "/c:/Users/<USER>/.cursor/extensions/ms-python.vscode-pylance-2024.8.1", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "ms-python", "metadata": {"installedTimestamp": 1748704530945, "source": "gallery", "id": "364d2426-116a-433a-a5d8-a5098dc3afbd", "publisherId": "998b010b-e2af-44a5-a6cd-0b5fd3b9b6f8", "publisherDisplayName": "ms-python", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 60897462}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "ms-python.black-formatter", "uuid": "859e640c-c157-47da-8699-9080b81c8371"}, "manifest": {"name": "black-formatter", "displayName": "<PERSON>atter", "description": "Formatting support for Python files using the Black formatter.", "version": "2025.2.0", "preview": true, "serverInfo": {"name": "Black", "module": "black"}, "publisher": "ms-python", "license": "MIT", "homepage": "https://github.com/Microsoft/vscode-black-formatter", "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-black-formatter.git"}, "bugs": {"url": "https://github.com/Microsoft/vscode-black-formatter/issues"}, "icon": "icon.png", "galleryBanner": {"color": "#1e415e", "theme": "dark"}, "keywords": ["python", "formatting", "black"], "engines": {"vscode": "^1.82.0"}, "categories": ["Programming Languages", "Formatters"], "extensionDependencies": ["ms-python.python"], "capabilities": {"virtualWorkspaces": {"supported": false, "description": "Virtual Workspaces are not supported with Black formatter."}}, "activationEvents": ["onLanguage:python", "workspaceContains:pyproject.toml", "workspaceContains:.black", "workspaceContains:*.py"], "main": "./dist/extension.js", "l10n": "./l10n", "scripts": {"compile": "webpack", "compile-tests": "tsc -p . --outDir out", "format-check": "prettier --check 'src/**/*.ts' 'build/**/*.yml' '.github/**/*.yml'", "lint": "eslint src --ext ts", "package": "webpack --mode production --devtool hidden-source-map", "pretest": "npm run compile-tests && npm run compile && npm run lint", "tests": "node ./out/test/ts_tests/runTest.js", "smoke-tests": "node ./out/test/ts_tests/runSmokeTest.js", "vsce-package": "vsce package -o black-formatter.vsix", "vscode:prepublish": "npm run package", "watch": "webpack --watch", "watch-tests": "tsc -p . -w --outDir out"}, "contributes": {"languages": [{"filenames": ["pyproject.toml", ".black"], "id": "toml"}], "configuration": {"properties": {"black-formatter.args": {"default": [], "items": {"type": "string"}, "markdownDescription": "Arguments passed to Black to format Python files. Each argument should be provided as a separate string in the array. \n Example: \n `\"black-formatter.args\" = [\"--config\", \"<file>\"]`", "scope": "resource", "type": "array"}, "black-formatter.cwd": {"default": "${workspaceFolder}", "markdownDescription": "Sets the current working directory used to format Python files with <PERSON>. By default, it uses the root directory of the workspace `${workspaceFolder}`. You can set it to `${fileDirname}` to use the parent folder of the file being formatted as the working directory for <PERSON>.", "scope": "resource", "type": "string", "examples": ["${workspaceFolder}/src", "${fileDirname}"]}, "black-formatter.importStrategy": {"default": "useBundled", "enum": ["fromEnvironment", "useBundled"], "markdownDescription": "Defines which Black formatter binary to be used to format Python files. When set to `useBundled`, the extension will use the Black formatter binary that is shipped with the extension. When set to `fromEnvironment`, the extension will attempt to use the Black formatter binary and all dependencies that are available in the currently selected environment. **Note**: If the extension can't find a valid Black formatter binary in the selected environment, it will fallback to using the binary that is shipped with the extension. The `black-formatter.path` setting takes precedence and overrides the behavior of `black-formatter.importStrategy`.", "markdownEnumDescriptions": ["Use the Black binary from the selected Python environment. If the extension fails to find a valid Black binary, it will fallback to using the bundled version of Black.", "Always use the bundled version of Black to format Python files."], "scope": "resource", "type": "string"}, "black-formatter.interpreter": {"default": [], "items": {"type": "string"}, "markdownDescription": "Path to a Python executable or a command that will be used to launch the Black server and any subprocess. Accepts an array of a single or multiple strings. When set to `[]`, the extension will use the path to the selected Python interpreter. If passing a command, each argument should be provided as a separate string in the array.", "scope": "resource", "type": "array"}, "black-formatter.path": {"default": [], "examples": [["-m", "black", "~/.env/python"], ["~/global_env/black"]], "items": {"type": "string"}, "markdownDescription": "Path or command to be used by the extension to format Python files with Black. Accepts an array of a single or multiple strings. If passing a command, each argument should be provided as a separate string in the array. If set to `[\"black\"]`, it will use the version of Black available in the `PATH` environment variable. Note: Using this option may slowdown formatting. \n  Examples: \n  - `[\"~/global_env/black\"]` \n  - `[\"conda\", \"run\", \"-n\", \"lint_env\", \"python\", \"-m\", \"black\"]`", "scope": "resource", "type": "array"}, "black-formatter.showNotifications": {"default": "off", "enum": ["always", "off", "onError", "onWarning"], "markdownDescription": "Controls when notifications are shown by this extension.", "markdownEnumDescriptions": ["Notifications are show for anything that the server chooses to show when formatting Python files.", "All notifications are turned off, any errors or warnings when formatting Python files are still available in the logs.", "Notifications are shown only in the case of an error when formatting Python files.", "Notifications are shown for any errors and warnings when formatting Python files."], "scope": "machine", "type": "string"}, "black-formatter.serverTransport": {"default": "stdio", "enum": ["stdio", "pipe"], "markdownDescription": "Selects the transport protocol to be used by the Black server. When set to `stdio`, the extension will use the standard input/output streams to communicate with the Black server. When set to `pipe`, the extension will use a named pipe (on Windows) or Unix Domain Socket (on Linux/Mac) to communicate with the Black server. The `stdio` transport protocol is the default and recommended option for most users.", "markdownEnumDescriptions": ["Use the standard input/output streams to communicate with the Black server.", "Use a named pipe (on windows) and Unix Domain Socket (on linux/mac) to communicate with the Black server."], "scope": "window", "type": "string"}}}, "commands": [{"title": "Restart Server", "category": "<PERSON>atter", "command": "black-formatter.restart"}]}, "dependencies": {"@vscode/python-extension": "^1.0.5", "fs-extra": "^11.2.0", "vscode-languageclient": "^9.0.1"}, "devDependencies": {"@types/chai": "^4.3.14", "@types/fs-extra": "^11.0.4", "@types/glob": "^8.1.0", "@types/mocha": "^10.0.6", "@types/node": "16.x", "@types/sinon": "^17.0.3", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "@vscode/test-electron": "^2.3.9", "@vscode/vsce": "^2.24.0", "chai": "^4.3.10", "eslint": "^8.57.0", "glob": "^10.3.12", "mocha": "^10.4.0", "prettier": "^3.2.5", "sinon": "^17.0.1", "ts-loader": "^9.5.1", "typemoq": "^2.1.0", "typescript": "^5.4.3", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}}, "location": {"$mid": 1, "path": "/c:/Users/<USER>/.cursor/extensions/ms-python.black-formatter-2025.2.0", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "ms-python", "metadata": {"installedTimestamp": 1749518916200, "pinned": false, "source": "gallery", "id": "859e640c-c157-47da-8699-9080b81c8371", "publisherId": "998b010b-e2af-44a5-a6cd-0b5fd3b9b6f8", "publisherDisplayName": "ms-python", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 3943976}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "ms-python.debugpy", "uuid": "4bd5d2c9-9d65-401a-b0b2-7498d9f17615"}, "manifest": {"name": "debugpy", "displayName": "Python Debugger", "description": "Python Debugger extension using debugpy.", "version": "2025.6.0", "publisher": "ms-python", "enabledApiProposals": ["portsAttributes", "debugVisualization", "contribViewsWelcome"], "license": "MIT", "homepage": "https://github.com/Microsoft/vscode-python-debugger", "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-python-debugger.git"}, "bugs": {"url": "https://github.com/Microsoft/vscode-python-debugger/issues"}, "icon": "icon.png", "keywords": ["python", "debugger", "debugpy"], "engines": {"vscode": "^1.92.0"}, "categories": ["Debuggers"], "activationEvents": ["onDebugInitialConfigurations", "onDebugDynamicConfigurations:debugpy", "onDebugResolve:debugpy", "onLanguage:python"], "main": "./dist/extension.js", "l10n": "./l10n", "contributes": {"commands": [{"category": "Python Debugger", "command": "debugpy.debugInTerminal", "icon": "$(debug-alt)", "title": "Python Debugger: Debug Python File"}, {"category": "Python Debugger", "command": "debugpy.debugUsingLaunchConfig", "icon": "$(debug-alt)", "title": "Python Debugger: Debug using launch.json"}, {"category": "Python Debugger", "command": "debugpy.clearCacheAndReload", "title": "Clear Cache and Reload Window"}, {"category": "Python Debugger", "command": "debugpy.viewOutput", "icon": {"dark": "resources/dark/repl.svg", "light": "resources/light/repl.svg"}, "title": "Show Output"}, {"category": "Python Debugger", "command": "debugpy.reportIssue", "title": "Report Issue..."}], "menus": {"issue/reporter": [{"command": "debugpy.reportIssue"}], "commandPalette": [{"category": "Python Debugger", "command": "debugpy.clearCacheAndReload", "title": "Clear Cache and Reload Window"}, {"category": "Python Debugger", "command": "debugpy.debugInTerminal", "icon": "$(debug-alt)", "title": "Python Debugger: Debug Python File", "when": "!virtualWorkspace && shellExecutionSupported && editorLangId == python"}, {"category": "Python Debugger", "command": "debugpy.debugUsingLaunchConfig", "icon": "$(debug-alt)", "title": "Python Debugger: Debug using launch.json", "when": "!virtualWorkspace && shellExecutionSupported && editorLangId == python"}, {"category": "Python Debugger", "command": "debugpy.viewOutput", "title": "Show Output"}, {"category": "Python Debugger", "command": "debugpy.reportIssue", "title": "Report Issue...", "when": "!virtualWorkspace && shellExecutionSupported"}], "editor/title/run": [{"command": "debugpy.debugInTerminal", "title": "Python Debugger: Debug Python File", "when": "resourceLangId == python && !isInDiffEditor && !virtualWorkspace && shellExecutionSupported"}, {"command": "debugpy.debugUsingLaunchConfig", "title": "Python Debugger: Debug using launch.json", "when": "resourceLangId == python && !isInDiffEditor && !virtualWorkspace && shellExecutionSupported"}]}, "configuration": {"properties": {"debugpy.debugJustMyCode": {"default": true, "description": "When debugging only step through user-written code. Disable this to allow stepping into library code.", "scope": "resource", "type": "boolean"}, "debugpy.showPythonInlineValues": {"default": false, "description": "Whether to display inline values in the editor while debugging.", "scope": "resource", "type": "boolean", "tags": ["experimental"]}}, "title": "Python Debugger", "type": "object"}, "debuggers": [{"configurationAttributes": {"attach": {"properties": {"connect": {"label": "Attach by connecting to debugpy over a socket.", "properties": {"host": {"default": "127.0.0.1", "description": "Hostname or IP address to connect to.", "type": "string"}, "port": {"description": "Port to connect to.", "type": ["number", "string"]}}, "required": ["port"], "type": "object"}, "debugAdapterPath": {"description": "Path (fully qualified) to the python debug adapter executable.", "type": "string"}, "django": {"default": false, "description": "Django debugging.", "type": "boolean"}, "jinja": {"default": null, "description": "Jinja template debugging (e.g. Flask).", "enum": [false, null, true]}, "justMyCode": {"default": true, "description": "If true, show and debug only user-written code. If false, show and debug all code, including library calls.", "type": "boolean"}, "listen": {"label": "Attach by listening for incoming socket connection from debugpy", "properties": {"host": {"default": "127.0.0.1", "description": "Hostname or IP address of the interface to listen on.", "type": "string"}, "port": {"description": "Port to listen on.", "type": ["number", "string"]}}, "required": ["port"], "type": "object"}, "logToFile": {"default": false, "description": "Enable logging of debugger events to a log file. This file can be found in the debugpy extension install folder.", "type": "boolean"}, "pathMappings": {"default": [], "items": {"label": "Path mapping", "properties": {"localRoot": {"default": "${workspaceFolder}", "label": "Local source root.", "type": "string"}, "remoteRoot": {"default": "", "label": "Remote source root.", "type": "string"}}, "required": ["localRoot", "remoteRoot"], "type": "object"}, "label": "Path mappings.", "type": "array"}, "processId": {"anyOf": [{"default": "${command:pickProcess}", "description": "Use process picker to select a process to attach, or Process ID as integer.", "enum": ["${command:pickProcess}"]}, {"description": "ID of the local process to attach to.", "type": "integer"}]}, "redirectOutput": {"default": true, "description": "Redirect output.", "type": "boolean"}, "showReturnValue": {"default": true, "description": "Show return value of functions when stepping.", "type": "boolean"}, "subProcess": {"default": false, "description": "Whether to enable Sub Process debugging", "type": "boolean"}, "consoleName": {"default": "Python Debug Console", "description": "Display name of the debug console or terminal", "type": "string"}, "clientOS": {"default": null, "description": "OS that VS code is using.", "enum": ["windows", null, "unix"]}}}, "launch": {"properties": {"args": {"default": [], "description": "Command line arguments passed to the program. For string type arguments, it will pass through the shell as is, and therefore all shell variable expansions will apply. But for the array type, the values will be shell-escaped.", "items": {"type": "string"}, "anyOf": [{"default": "${command:pickArgs}", "enum": ["${command:pickArgs}"]}, {"type": ["array", "string"]}]}, "autoReload": {"default": {}, "description": "Configures automatic reload of code on edit.", "properties": {"enable": {"default": false, "description": "Automatically reload code on edit.", "type": "boolean"}, "exclude": {"default": ["**/.git/**", "**/.metadata/**", "**/__pycache__/**", "**/node_modules/**", "**/site-packages/**"], "description": "Glob patterns of paths to exclude from auto reload.", "items": {"type": "string"}, "type": "array"}, "include": {"default": ["**/*.py", "**/*.pyw"], "description": "Glob patterns of paths to include in auto reload.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "console": {"default": "integratedTerminal", "description": "Where to launch the debug target: internal console, integrated terminal, or external terminal.", "enum": ["externalTerminal", "integratedTerminal", "internalConsole"]}, "cwd": {"default": "${workspaceFolder}", "description": "Absolute path to the working directory of the program being debugged. Default is the root directory of the file (leave empty).", "type": "string"}, "debugAdapterPath": {"description": "Path (fully qualified) to the Python debug adapter executable.", "type": "string"}, "autoStartBrowser": {"default": false, "description": "Open external browser to launch the application", "type": "boolean"}, "django": {"default": false, "description": "Django debugging.", "type": "boolean"}, "env": {"additionalProperties": {"type": "string"}, "default": {}, "description": "Environment variables defined as a key value pair. Property ends up being the Environment Variable and the value of the property ends up being the value of the Env Variable.", "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "Absolute path to a file containing environment variable definitions.", "type": "string"}, "gevent": {"default": false, "description": "Enable debugging of gevent monkey-patched code.", "type": "boolean"}, "jinja": {"default": null, "description": "Jinja template debugging (e.g. Flask).", "enum": [false, null, true]}, "justMyCode": {"default": true, "description": "Debug only user-written code.", "type": "boolean"}, "logToFile": {"default": false, "description": "Enable logging of debugger events to a log file. This file can be found in the debugpy extension install folder.", "type": "boolean"}, "module": {"default": "", "description": "Name of the module to be debugged.", "type": "string"}, "pathMappings": {"default": [], "items": {"label": "Path mapping", "properties": {"localRoot": {"default": "${workspaceFolder}", "label": "Local source root.", "type": "string"}, "remoteRoot": {"default": "", "label": "Remote source root.", "type": "string"}}, "required": ["localRoot", "remoteRoot"], "type": "object"}, "label": "Path mappings.", "type": "array"}, "program": {"default": "${file}", "description": "Absolute path to the program.", "type": "string"}, "purpose": {"default": [], "description": "Tells extension to use this configuration for test debugging, or when using debug-in-terminal command.", "items": {"enum": ["debug-test", "debug-in-terminal"], "enumDescriptions": ["Use this configuration while debugging tests using test view or test debug commands.", "Use this configuration while debugging a file using debug in terminal button in the editor."]}, "type": "array"}, "pyramid": {"default": false, "description": "Whether debugging Pyramid applications.", "type": "boolean"}, "python": {"default": "${command:python.interpreterPath}", "description": "Absolute path to the Python interpreter executable; overrides workspace configuration if set.", "type": "string"}, "pythonArgs": {"default": [], "description": "Command-line arguments passed to the Python interpreter. To pass arguments to the debug target, use \"args\".", "items": {"type": "string"}, "type": "array"}, "redirectOutput": {"default": true, "description": "Redirect output.", "type": "boolean"}, "showReturnValue": {"default": true, "description": "Show return value of functions when stepping.", "type": "boolean"}, "stopOnEntry": {"default": false, "description": "Automatically stop after launch.", "type": "boolean"}, "subProcess": {"default": false, "description": "Whether to enable Sub Process debugging.", "type": "boolean"}, "sudo": {"default": false, "description": "Running debug program under elevated permissions (on Unix).", "type": "boolean"}, "guiEventLoop": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "The GUI event loop that's going to run. Possible values: \"matplotlib\", \"wx\", \"qt\", \"none\", or a custom function that'll be imported and run.", "type": "string"}, "consoleName": {"default": "Python Debug Console", "description": "Display name of the debug console or terminal", "type": "string"}, "clientOS": {"default": null, "description": "OS that VS code is using.", "enum": ["windows", null, "unix"]}}}}, "configurationSnippets": [], "label": "Python Debugger", "languages": ["python"], "type": "debugpy", "variables": {"pickProcess": "debugpy.pickLocalProcess", "pickArgs": "debugpy.pickArgs"}, "when": "!virtualWorkspace && shellExecutionSupported"}], "debugVisualizers": [{"id": "inlineHexDecoder", "when": "debugConfigurationType == 'debugpy' && (variableType == 'float' || variableType == 'int')"}], "viewsWelcome": [{"view": "debug", "contents": "\n[Show automatic Python configurations](command:workbench.action.debug.selectandstart?%5B%22debugpy%22%5D)\n", "when": "dynamicPythonConfigAvailable && !virtualWorkspace"}]}, "extensionDependencies": ["ms-python.python"], "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "format-check": "prettier --check 'src/**/*.ts' 'build/**/*.yml' '.github/**/*.yml'", "format-fix": "prettier --write 'src/**/*.ts' 'build/**/*.yml' '.github/**/*.yml'", "test": "node ./out/test/runTest.js", "vsce-package": "npx @vscode/vsce package -o python-debugger.vsix"}, "devDependencies": {"@types/chai": "^4.3.4", "@types/chai-as-promised": "^7.1.8", "@types/fs-extra": "^11.0.4", "@types/glob": "^7.2.0", "@types/lodash": "^4.14.191", "@types/mocha": "^10.0.7", "@types/node": "18.x", "@types/semver": "^7.3.13", "@types/sinon": "^10.0.13", "@types/vscode": "^1.87.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vscode/test-electron": "^2.3.9", "@vscode/vsce": "^2.24.0", "chai": "^4.3.7", "chai-as-promised": "^7.1.1", "eslint": "^8.50.0", "glob": "^8.0.3", "mocha": "^10.7.0", "prettier": "^3.0.3", "semver": "^7.5.4", "sinon": "^15.0.2", "ts-loader": "^9.3.1", "ts-mockito": "^2.6.1", "typemoq": "^2.1.0", "typescript": "^5.5.4", "webpack": "^5.87.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@vscode/debugadapter": "^1.65.0", "@vscode/debugprotocol": "^1.65.0", "@vscode/extension-telemetry": "^0.8.5", "@vscode/python-extension": "^1.0.5", "fs-extra": "^11.2.0", "iconv-lite": "^0.6.3", "jsonc-parser": "^3.2.0", "lodash": "^4.17.21", "vscode-languageclient": "^8.0.2"}}, "location": {"$mid": 1, "path": "/c:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.6.0-win32-x64", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "win32-x64", "publisherDisplayName": "ms-python", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1750448476950, "pinned": false, "source": "gallery", "id": "4bd5d2c9-9d65-401a-b0b2-7498d9f17615", "publisherId": "998b010b-e2af-44a5-a6cd-0b5fd3b9b6f8", "publisherDisplayName": "ms-python", "targetPlatform": "win32-x64", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false, "size": 60227892}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "ms-python.python", "uuid": "f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5"}, "manifest": {"name": "python", "displayName": "Python", "description": "Python language support with extension access points for IntelliSense (Pylance), Debugging (Python Debugger), linting, formatting, refactoring, unit tests, and more.", "version": "2025.6.1", "featureFlags": {"usingNewInterpreterStorage": true}, "capabilities": {"untrustedWorkspaces": {"supported": "limited", "description": "Only Partial IntelliSense with Pylance is supported. Cannot execute Python with untrusted files."}, "virtualWorkspaces": {"supported": "limited", "description": "Only Partial IntelliSense supported."}}, "publisher": "ms-python", "enabledApiProposals": ["contribEditorContentMenu", "quickPickSortByLabel", "testObserver", "quickPickItemTooltip", "terminalDataWriteEvent", "terminalExecuteCommandEvent", "codeActionAI", "notebookReplDocument", "notebookVariableProvider"], "author": {"name": "Microsoft Corporation"}, "license": "MIT", "homepage": "https://github.com/Microsoft/vscode-python", "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-python"}, "bugs": {"url": "https://github.com/Microsoft/vscode-python/issues"}, "qna": "https://github.com/microsoft/vscode-python/discussions/categories/q-a", "icon": "icon.png", "galleryBanner": {"color": "#1e415e", "theme": "dark"}, "engines": {"vscode": "^1.94.0-20240918"}, "enableTelemetry": true, "keywords": ["python", "django", "unittest", "multi-root ready"], "categories": ["Programming Languages", "Debuggers", "Other", "Data Science", "Machine Learning"], "activationEvents": ["onDebugInitialConfigurations", "onLanguage:python", "onDebugResolve:python", "onCommand:python.copilotSetupTests", "workspaceContains:mspythonconfig.json", "workspaceContains:pyproject.toml", "workspaceContains:Pipfile", "workspaceContains:setup.py", "workspaceContains:requirements.txt", "workspaceContains:manage.py", "workspaceContains:app.py", "workspaceContains:.venv", "workspaceContains:.conda"], "main": "./out/client/extension", "browser": "./dist/extension.browser.js", "l10n": "./l10n", "contributes": {"problemMatchers": [{"name": "python", "owner": "python", "source": "python", "fileLocation": "autoDetect", "pattern": [{"regexp": "^.*File \\\"([^\\\"]|.*)\\\", line (\\d+).*", "file": 1, "line": 2}, {"regexp": "^\\s*(.*)\\s*$"}, {"regexp": "^\\s*(.*Error.*)$", "message": 1}]}], "walkthroughs": [{"id": "pythonWelcome", "title": "Get Started with Python Development", "description": "Your first steps to set up a Python project with all the powerful tools and features that the Python extension has to offer!", "when": "workspacePlatform != webworker", "steps": [{"id": "python.createPythonFolder", "title": "Open a Python project folder", "description": "[Open](command:workbench.action.files.openFolder) or create a project folder.\n[Open Project Folder](command:workbench.action.files.openFolder)", "media": {"svg": "resources/walkthrough/open-folder.svg", "altText": "Open a Python file or a folder with a Python project."}, "when": "workspaceFolderCount = 0"}, {"id": "python.createPythonFile", "title": "Create a Python file", "description": "[Open](command:toSide:workbench.action.files.openFile) or [create](command:toSide:workbench.action.files.newUntitledFile?%7B%22languageId%22%3A%22python%22%7D) a Python file - make sure to save it as \".py\".\n[Create Python File](command:toSide:workbench.action.files.newUntitledFile?%7B%22languageId%22%3A%22python%22%7D)", "media": {"svg": "resources/walkthrough/open-folder.svg", "altText": "Open a Python file or a folder with a Python project."}}, {"id": "python.installPythonWin8", "title": "Install Python", "description": "The Python Extension requires Python to be installed. Install Python [from python.org](https://www.python.org/downloads).\n\n[Install Python](https://www.python.org/downloads)\n", "media": {"markdown": "resources/walkthrough/install-python-windows-8.md"}, "when": "workspacePlatform == windows && showInstallPythonTile"}, {"id": "python.installPythonMac", "title": "Install Python", "description": "The Python Extension requires Python to be installed. Install Python 3 through the terminal.\n[Install Python via <PERSON><PERSON>](command:python.installPythonOnMac)\n", "media": {"markdown": "resources/walkthrough/install-python-macos.md"}, "when": "workspacePlatform == mac && showInstallPythonTile", "command": "workbench.action.terminal.new"}, {"id": "python.installPythonLinux", "title": "Install Python", "description": "The Python Extension requires Python to be installed. Install Python 3 through the terminal.\n[Install Python via terminal](command:python.installPythonOnLinux)\n", "media": {"markdown": "resources/walkthrough/install-python-linux.md"}, "when": "workspacePlatform == linux && showInstallPythonTile", "command": "workbench.action.terminal.new"}, {"id": "python.createEnvironment", "title": "Select or create a Python environment", "description": "Create an environment for your Python project or use [Select Python Interpreter](command:python.setInterpreter) to select an existing one.\n[Create Environment](command:python.createEnvironment)\n**Tip**: Run the ``Python: Create Environment`` command in the [Command Palette](command:workbench.action.showCommands).", "media": {"svg": "resources/walkthrough/create-environment.svg", "altText": "Creating a Python environment from the Command Palette"}}, {"id": "python.runAndDebug", "title": "Run and debug your Python file", "description": "Open your Python file  and click on the play button on the top right of the editor, or press F5 when on the file and select \"Python File\" to run with the debugger. \n  \n[Learn more](https://code.visualstudio.com/docs/python/python-tutorial#_run-hello-world)", "media": {"svg": "resources/walkthrough/rundebug2.svg", "altText": "How to run and debug in VS Code with F5 or the play button on the top right."}}, {"id": "python.learnMoreWithDS", "title": "Keep exploring!", "description": "🎨 Explore all the features the Python extension has to offer by looking for \"Python\" in the [Command Palette](command:workbench.action.showCommands). \n 📈 Learn more about getting started with [data science](command:workbench.action.openWalkthrough?%7B%22category%22%3A%22ms-python.python%23pythonDataScienceWelcome%22%2C%22step%22%3A%22ms-python.python%23python.createNewNotebook%22%7D) in Python. \n ✨ Take a look at our [Release Notes](https://aka.ms/AA8dxtb) to learn more about the latest features. \n \n[Follow along with the Python Tutorial](https://aka.ms/AA8dqti)", "media": {"altText": "Image representing our documentation page and mailing list resources.", "svg": "resources/walkthrough/learnmore.svg"}}]}, {"id": "pythonDataScienceWelcome", "title": "Get Started with Python for Data Science", "description": "Your first steps to getting started with a Data Science project with Python!", "when": "false", "steps": [{"id": "python.installJupyterExt", "title": "Install Jupyter extension", "description": "If you haven't already, install the [<PERSON><PERSON><PERSON> extension](command:workbench.extensions.search?\"ms-toolsai.jupyter\") to take full advantage of notebooks experiences in VS Code!\n \n[Search Jupyter extension](command:workbench.extensions.search?\"ms-toolsai.jupyter\")", "media": {"svg": "resources/walkthrough/data-science.svg", "altText": "Creating a new <PERSON><PERSON><PERSON> notebook"}}, {"id": "python.createNewNotebook", "title": "Create or open a Jupyter Notebook", "description": "Right click in the file explorer and create a new file with an .ipynb extension. Or, open the [Command Palette](command:workbench.action.showCommands) and run the command \n``Jupyter: Create New Blank Notebook``.\n[Create new Jupyter Notebook](command:toSide:jupyter.createnewnotebook)\n If you have an existing project, you can also [open a folder](command:workbench.action.files.openFolder) and/or clone a project from GitHub: [clone a Git repository](command:git.clone).", "media": {"svg": "resources/walkthrough/create-notebook.svg", "altText": "Creating a new <PERSON><PERSON><PERSON> notebook"}, "completionEvents": ["onCommand:jupyter.createnewnotebook", "onCommand:workbench.action.files.openFolder", "onCommand:workbench.action.files.openFileFolder"]}, {"id": "python.openInteractiveWindow", "title": "Open the Python Interactive Window", "description": "The Python Interactive Window is a Python shell where you can execute and view the results of your Python code. You can create cells on a Python file by typing ``#%%``.\n \nTo open the interactive window anytime, open the [Command Palette](command:workbench.action.showCommands) and run the command \n``Jupyter: Create Interactive Window``.\n[Open Interactive Window](command:jupyter.createnewinteractive)", "media": {"svg": "resources/walkthrough/interactive-window.svg", "altText": "Opening Python interactive window"}, "completionEvents": ["onCommand:jupyter.createnewinteractive"]}, {"id": "python.dataScienceLearnMore", "title": "Find out more!", "description": "📒 Take a look into the [<PERSON><PERSON><PERSON> extension](command:workbench.extensions.search?\"ms-toolsai.jupyter\") features, by looking for \"Jupyter\" in the [Command Palette](command:workbench.action.showCommands). \n 🏃🏻 Find out more features in our [Tutorials](https://aka.ms/AAdjzpd).  \n[Learn more](https://aka.ms/AAdar6q)", "media": {"svg": "resources/walkthrough/learnmore.svg", "altText": "Image representing our documentation page and mailing list resources."}}]}], "breakpoints": [{"language": "html"}, {"language": "jinja"}, {"language": "python"}, {"language": "django-html"}, {"language": "django-txt"}], "commands": [{"title": "New Python File", "shortTitle": "Python File", "category": "Python", "command": "python.createNewFile"}, {"category": "Python", "command": "python.analysis.restartLanguageServer", "title": "Restart Language Server"}, {"category": "Python", "command": "python.clearCacheAndReload", "title": "Clear Cache and Reload Window"}, {"category": "Python", "command": "python.clearWorkspaceInterpreter", "title": "Clear Workspace Interpreter Setting"}, {"category": "Python", "command": "python.configureTests", "title": "Configure Tests"}, {"category": "Python", "command": "python.createTerminal", "title": "Create Terminal"}, {"category": "Python", "command": "python.createEnvironment", "title": "Create Environment..."}, {"category": "Python", "command": "python.createEnvironment-button", "title": "Create Environment..."}, {"category": "Python", "command": "python.execInTerminal", "title": "Run Python File in Terminal"}, {"category": "Python", "command": "python.execInTerminal-icon", "icon": "$(play)", "title": "Run Python File"}, {"category": "Python", "command": "python.execInDedicatedTerminal", "icon": "$(play)", "title": "Run Python File in Dedicated Terminal"}, {"category": "Python", "command": "python.execSelectionInDjangoShell", "title": "Run Selection/Line in Django Shell"}, {"category": "Python", "command": "python.execSelectionInTerminal", "title": "Run Selection/Line in Python Terminal"}, {"category": "Python", "command": "python.execInREPL", "title": "Run Selection/Line in Native Python REPL"}, {"category": "Python", "command": "python.reportIssue", "title": "Report Issue..."}, {"category": "Test", "command": "testing.reRunFailTests", "icon": "$(run-errors)", "title": "<PERSON><PERSON> Failed Tests"}, {"category": "Python", "command": "python.setInterpreter", "title": "Select Interpreter"}, {"category": "Python", "command": "python.startREPL", "title": "Start Terminal REPL"}, {"category": "Python", "command": "python.startNativeREPL", "title": "Start Native Python REPL"}, {"category": "Python", "command": "python.viewLanguageServerOutput", "enablement": "python.hasLanguageServerOutputChannel", "title": "Show Language Server Output"}, {"category": "Python", "command": "python.viewOutput", "icon": {"dark": "resources/dark/repl.svg", "light": "resources/light/repl.svg"}, "title": "Show Output"}, {"category": "Python", "command": "python.installJupyter", "title": "Install the Jupyter extension"}], "configuration": {"properties": {"python.activeStateToolPath": {"default": "state", "description": "Path to the State Tool executable for ActiveState runtimes (version 0.36+).", "scope": "machine-overridable", "type": "string"}, "python.autoComplete.extraPaths": {"default": [], "description": "List of paths to libraries and the like that need to be imported by auto complete engine. E.g. when using Google App SDK, the paths are not in system path, hence need to be added into this list.", "scope": "resource", "type": "array", "uniqueItems": true}, "python.createEnvironment.contentButton": {"default": "hide", "markdownDescription": "Show or hide Create Environment button in the editor for `requirements.txt` or other dependency files.", "scope": "machine-overridable", "type": "string", "enum": ["show", "hide"]}, "python.createEnvironment.trigger": {"default": "prompt", "markdownDescription": "Detect if environment creation is required for the current project", "scope": "machine-overridable", "type": "string", "enum": ["off", "prompt"]}, "python.condaPath": {"default": "", "description": "Path to the conda executable to use for activation (version 4.4+).", "scope": "machine", "type": "string"}, "python.defaultInterpreterPath": {"default": "python", "markdownDescription": "Path to default Python to use when extension loads up for the first time, no longer used once an interpreter is selected for the workspace. See [here](https://aka.ms/AAfekmf) to understand when this is used", "scope": "machine-overridable", "type": "string"}, "python.envFile": {"default": "${workspaceFolder}/.env", "description": "Absolute path to a file containing environment variable definitions.", "scope": "resource", "type": "string"}, "python.experiments.enabled": {"default": true, "description": "Enables A/B tests experiments in the Python extension. If enabled, you may get included in proposed enhancements and/or features.", "scope": "window", "type": "boolean"}, "python.experiments.optInto": {"default": [], "markdownDescription": "List of experiments to opt into. If empty, user is assigned the default experiment groups. See [here](https://github.com/microsoft/vscode-python/wiki/AB-Experiments) for more details.", "items": {"enum": ["All", "pythonSurveyNotification", "pythonPromptNewToolsExt", "pythonTerminalEnvVarActivation", "pythonDiscoveryUsingWorkers", "pythonTestAdapter"], "enumDescriptions": ["Combined list of all experiments.", "Denotes the Python Survey Notification experiment.", "Denotes the Python Prompt New Tools Extension experiment.", "Enables use of environment variables to activate terminals instead of sending activation commands.", "Enables use of worker threads to do heavy computation when discovering interpreters.", "Denotes the Python Test Adapter experiment."]}, "scope": "window", "type": "array", "uniqueItems": true}, "python.experiments.optOutFrom": {"default": [], "markdownDescription": "List of experiments to opt out of. If empty, user is assigned the default experiment groups. See [here](https://github.com/microsoft/vscode-python/wiki/AB-Experiments) for more details.", "items": {"enum": ["All", "pythonSurveyNotification", "pythonPromptNewToolsExt", "pythonTerminalEnvVarActivation", "pythonDiscoveryUsingWorkers", "pythonTestAdapter"], "enumDescriptions": ["Combined list of all experiments.", "Denotes the Python Survey Notification experiment.", "Denotes the Python Prompt New Tools Extension experiment.", "Enables use of environment variables to activate terminals instead of sending activation commands.", "Enables use of worker threads to do heavy computation when discovering interpreters.", "Denotes the Python Test Adapter experiment."]}, "scope": "window", "type": "array", "uniqueItems": true}, "python.globalModuleInstallation": {"default": false, "description": "Whether to install Python modules globally when not using an environment.", "scope": "resource", "type": "boolean"}, "python.languageServer": {"default": "<PERSON><PERSON><PERSON>", "description": "Defines type of the language server.", "enum": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "None"], "enumDescriptions": ["Automatically select a language server: <PERSON><PERSON>nce if installed and available, otherwise fallback to Jedi.", "Use Jedi behind the Language Server Protocol (LSP) as a language server.", "Use Pylance as a language server.", "Disable language server capabilities."], "scope": "window", "type": "string"}, "python.interpreter.infoVisibility": {"default": "onPythonRelated", "description": "Controls when to display information of selected interpreter in the status bar.", "enum": ["never", "onPythonRelated", "always"], "enumDescriptions": ["Never display information.", "Only display information if Python-related files are opened.", "Always display information."], "scope": "machine", "type": "string"}, "python.logging.level": {"default": "error", "deprecationMessage": "This setting is deprecated. Please use command `Developer: Set Log Level...` to set logging level.", "description": "The logging level the extension logs at, defaults to 'error'", "enum": ["debug", "error", "info", "off", "warn"], "scope": "machine", "type": "string"}, "python.missingPackage.severity": {"default": "Hint", "description": "Set severity of missing packages in requirements.txt or pyproject.toml", "enum": ["Error", "Hint", "Information", "Warning"], "scope": "resource", "type": "string"}, "python.locator": {"default": "js", "description": "[Experimental] Select implementation of environment locators. This is an experimental setting while we test native environment location.", "enum": ["js", "native"], "tags": ["onExP", "preview"], "scope": "machine", "type": "string"}, "python.pipenvPath": {"default": "pipenv", "description": "Path to the pipenv executable to use for activation.", "scope": "machine-overridable", "type": "string"}, "python.poetryPath": {"default": "poetry", "description": "Path to the poetry executable.", "scope": "machine-overridable", "type": "string"}, "python.pixiToolPath": {"default": "pixi", "description": "Path to the pixi executable.", "scope": "machine-overridable", "type": "string"}, "python.terminal.activateEnvInCurrentTerminal": {"default": false, "description": "Activate Python Environment in the current Terminal on load of the Extension.", "scope": "resource", "type": "boolean"}, "python.terminal.activateEnvironment": {"default": true, "description": "Activate Python Environment in all Terminals created.", "scope": "resource", "type": "boolean"}, "python.terminal.executeInFileDir": {"default": false, "description": "When executing a file in the terminal, whether to use execute in the file's directory, instead of the current open folder.", "scope": "resource", "type": "boolean"}, "python.terminal.focusAfterLaunch": {"default": false, "description": "When launching a python terminal, whether to focus the cursor on the terminal.", "scope": "resource", "type": "boolean"}, "python.terminal.launchArgs": {"default": [], "description": "Python launch arguments to use when executing a file in the terminal.", "scope": "resource", "type": "array"}, "python.terminal.shellIntegration.enabled": {"default": false, "markdownDescription": "Enable [shell integration](https://code.visualstudio.com/docs/terminal/shell-integration) for the terminals running python. Shell integration enhances the terminal experience by enabling command decorations, run recent command, improving accessibility among other things.", "scope": "resource", "type": "boolean", "tags": ["preview"]}, "python.REPL.enableREPLSmartSend": {"default": true, "description": "Toggle Smart Send for the Python REPL. Smart Send enables sending the smallest runnable block of code to the REPL on Shift+Enter and moves the cursor accordingly.", "scope": "resource", "type": "boolean"}, "python.REPL.sendToNativeREPL": {"default": false, "description": "Toggle to send code to Python REPL instead of the terminal on execution. Turning this on will change the behavior for both Smart Send and Run Selection/Line in the Context Menu.", "scope": "resource", "type": "boolean"}, "python.REPL.provideVariables": {"default": true, "description": "Toggle to provide variables for the REPL variable view for the native REPL.", "scope": "resource", "type": "boolean"}, "python.testing.autoTestDiscoverOnSaveEnabled": {"default": true, "description": "Enable auto run test discovery when saving a test file.", "scope": "resource", "type": "boolean"}, "python.testing.autoTestDiscoverOnSavePattern": {"default": "**/*.py", "description": "Glob pattern used to determine which files are used by autoTestDiscoverOnSaveEnabled.", "scope": "resource", "type": "string"}, "python.testing.cwd": {"default": null, "description": "Optional working directory for tests.", "scope": "resource", "type": "string"}, "python.testing.debugPort": {"default": 3000, "description": "Port number used for debugging of tests.", "scope": "resource", "type": "number"}, "python.testing.promptToConfigure": {"default": true, "description": "Prompt to configure a test framework if potential tests directories are discovered.", "scope": "resource", "type": "boolean"}, "python.testing.pytestArgs": {"default": [], "description": "Arguments passed in. Each argument is a separate item in the array.", "items": {"type": "string"}, "scope": "resource", "type": "array"}, "python.testing.pytestEnabled": {"default": false, "description": "Enable testing using pytest.", "scope": "resource", "type": "boolean"}, "python.testing.pytestPath": {"default": "pytest", "description": "Path to pytest. You can use a custom version of pytest by modifying this setting to include the full path.", "scope": "machine-overridable", "type": "string"}, "python.testing.unittestArgs": {"default": ["-v", "-s", ".", "-p", "*test*.py"], "description": "Arguments passed in. Each argument is a separate item in the array.", "items": {"type": "string"}, "scope": "resource", "type": "array"}, "python.testing.unittestEnabled": {"default": false, "description": "Enable testing using unittest.", "scope": "resource", "type": "boolean"}, "python.venvFolders": {"default": [], "description": "Folders in your home directory to look into for virtual environments (supports pyenv, direnv and virtualenvwrapper by default).", "items": {"type": "string"}, "scope": "machine", "type": "array", "uniqueItems": true}, "python.venvPath": {"default": "", "description": "Path to folder with a list of Virtual Environments (e.g. ~/.pyenv, ~/Envs, ~/.virtualenvs).", "scope": "machine", "type": "string"}}, "title": "Python", "type": "object"}, "debuggers": [{"configurationAttributes": {"attach": {"properties": {"connect": {"label": "Attach by connecting to debugpy over a socket.", "properties": {"host": {"default": "127.0.0.1", "description": "Hostname or IP address to connect to.", "type": "string"}, "port": {"description": "Port to connect to.", "type": "number"}}, "required": ["port"], "type": "object"}, "debugAdapterPath": {"description": "Path (fully qualified) to the python debug adapter executable.", "type": "string"}, "django": {"default": false, "description": "Django debugging.", "type": "boolean"}, "host": {"default": "127.0.0.1", "description": "Hostname or IP address to connect to.", "type": "string"}, "jinja": {"default": null, "description": "Jinja template debugging (e.g. Flask).", "enum": [false, null, true]}, "justMyCode": {"default": true, "description": "If true, show and debug only user-written code. If false, show and debug all code, including library calls.", "type": "boolean"}, "listen": {"label": "Attach by listening for incoming socket connection from debugpy", "properties": {"host": {"default": "127.0.0.1", "description": "Hostname or IP address of the interface to listen on.", "type": "string"}, "port": {"description": "Port to listen on.", "type": "number"}}, "required": ["port"], "type": "object"}, "logToFile": {"default": false, "description": "Enable logging of debugger events to a log file.", "type": "boolean"}, "pathMappings": {"default": [], "items": {"label": "Path mapping", "properties": {"localRoot": {"default": "${workspaceFolder}", "label": "Local source root.", "type": "string"}, "remoteRoot": {"default": "", "label": "Remote source root.", "type": "string"}}, "required": ["localRoot", "remoteRoot"], "type": "object"}, "label": "Path mappings.", "type": "array"}, "port": {"description": "Port to connect to.", "type": "number"}, "processId": {"anyOf": [{"default": "${command:pickProcess}", "description": "Use process picker to select a process to attach, or Process ID as integer.", "enum": ["${command:pickProcess}"]}, {"description": "ID of the local process to attach to.", "type": "integer"}]}, "redirectOutput": {"default": true, "description": "Redirect output.", "type": "boolean"}, "showReturnValue": {"default": true, "description": "Show return value of functions when stepping.", "type": "boolean"}, "subProcess": {"default": false, "description": "Whether to enable Sub Process debugging", "type": "boolean"}}}, "launch": {"properties": {"args": {"default": [], "description": "Command line arguments passed to the program.", "items": {"type": "string"}, "type": ["array", "string"]}, "autoReload": {"default": {}, "description": "Configures automatic reload of code on edit.", "properties": {"enable": {"default": false, "description": "Automatically reload code on edit.", "type": "boolean"}, "exclude": {"default": ["**/.git/**", "**/.metadata/**", "**/__pycache__/**", "**/node_modules/**", "**/site-packages/**"], "description": "Glob patterns of paths to exclude from auto reload.", "items": {"type": "string"}, "type": "array"}, "include": {"default": ["**/*.py", "**/*.pyw"], "description": "Glob patterns of paths to include in auto reload.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "console": {"default": "integratedTerminal", "description": "Where to launch the debug target: internal console, integrated terminal, or external terminal.", "enum": ["externalTerminal", "integratedTerminal", "internalConsole"]}, "consoleTitle": {"default": "Python Debug Console", "description": "Display name of the debug console or terminal"}, "cwd": {"default": "${workspaceFolder}", "description": "Absolute path to the working directory of the program being debugged. Default is the root directory of the file (leave empty).", "type": "string"}, "debugAdapterPath": {"description": "Path (fully qualified) to the python debug adapter executable.", "type": "string"}, "django": {"default": false, "description": "Django debugging.", "type": "boolean"}, "env": {"additionalProperties": {"type": "string"}, "default": {}, "description": "Environment variables defined as a key value pair. Property ends up being the Environment Variable and the value of the property ends up being the value of the Env Variable.", "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "Absolute path to a file containing environment variable definitions.", "type": "string"}, "gevent": {"default": false, "description": "Enable debugging of gevent monkey-patched code.", "type": "boolean"}, "host": {"default": "localhost", "description": "IP address of the of the local debug server (default is localhost).", "type": "string"}, "jinja": {"default": null, "description": "Jinja template debugging (e.g. Flask).", "enum": [false, null, true]}, "justMyCode": {"default": true, "description": "Debug only user-written code.", "type": "boolean"}, "logToFile": {"default": false, "description": "Enable logging of debugger events to a log file.", "type": "boolean"}, "module": {"default": "", "description": "Name of the module to be debugged.", "type": "string"}, "pathMappings": {"default": [], "items": {"label": "Path mapping", "properties": {"localRoot": {"default": "${workspaceFolder}", "label": "Local source root.", "type": "string"}, "remoteRoot": {"default": "", "label": "Remote source root.", "type": "string"}}, "required": ["localRoot", "remoteRoot"], "type": "object"}, "label": "Path mappings.", "type": "array"}, "port": {"default": 0, "description": "Debug port (default is 0, resulting in the use of a dynamic port).", "type": "number"}, "program": {"default": "${file}", "description": "Absolute path to the program.", "type": "string"}, "purpose": {"default": [], "description": "Tells extension to use this configuration for test debugging, or when using debug-in-terminal command.", "items": {"enum": ["debug-test", "debug-in-terminal"], "enumDescriptions": ["Use this configuration while debugging tests using test view or test debug commands.", "Use this configuration while debugging a file using debug in terminal button in the editor."]}, "type": "array"}, "pyramid": {"default": false, "description": "Whether debugging Pyramid applications", "type": "boolean"}, "python": {"default": "${command:python.interpreterPath}", "description": "Absolute path to the Python interpreter executable; overrides workspace configuration if set.", "type": "string"}, "pythonArgs": {"default": [], "description": "Command-line arguments passed to the Python interpreter. To pass arguments to the debug target, use \"args\".", "items": {"type": "string"}, "type": "array"}, "redirectOutput": {"default": true, "description": "Redirect output.", "type": "boolean"}, "showReturnValue": {"default": true, "description": "Show return value of functions when stepping.", "type": "boolean"}, "stopOnEntry": {"default": false, "description": "Automatically stop after launch.", "type": "boolean"}, "subProcess": {"default": false, "description": "Whether to enable Sub Process debugging", "type": "boolean"}, "sudo": {"default": false, "description": "Running debug program under elevated permissions (on Unix).", "type": "boolean"}}}}, "deprecated": "This configuration will be deprecated soon. Please replace `python` with `debugpy` to use the new Python Debugger extension.", "configurationSnippets": [], "label": "Python", "languages": ["python"], "type": "python", "variables": {"pickProcess": "python.pickLocalProcess"}, "when": "!virtualWorkspace && shellExecutionSupported", "hiddenWhen": "true"}], "grammars": [{"language": "pip-requirements", "path": "./syntaxes/pip-requirements.tmLanguage.json", "scopeName": "source.pip-requirements"}], "jsonValidation": [{"fileMatch": ".condarc", "url": "./schemas/condarc.json"}, {"fileMatch": "environment.yml", "url": "./schemas/conda-environment.json"}, {"fileMatch": "meta.yaml", "url": "./schemas/conda-meta.json"}], "keybindings": [{"command": "python.execSelectionInTerminal", "key": "shift+enter", "when": "editorTextFocus && editorLangId == python && !findInputFocussed && !replaceInputFocussed && !jupyter.ownsSelection && !notebookEditorFocused && !isCompositeNotebook"}, {"command": "python.execInREPL", "key": "shift+enter", "when": "config.python.REPL.sendToNativeREPL && editorLangId == python && editorTextFocus && !jupyter.ownsSelection && !notebookEditorFocused && !isCompositeNotebook"}, {"command": "python.execInREPLEnter", "key": "enter", "when": "!config.interactiveWindow.executeWithShiftEnter && isCompositeNotebook && activeEditor == 'workbench.editor.repl' && !inlineChatFocused && !notebookCellListFocused"}, {"command": "python.execInInteractiveWindowEnter", "key": "enter", "when": "!config.interactiveWindow.executeWithShiftEnter && isCompositeNotebook && activeEditor == 'workbench.editor.interactive' && !inlineChatFocused && !notebookCellListFocused"}], "languages": [{"aliases": ["<PERSON><PERSON>"], "extensions": [".j2", ".jinja2"], "id": "jinja"}, {"aliases": ["pip requirements", "requirements.txt"], "configuration": "./languages/pip-requirements.json", "filenamePatterns": ["**/*requirements*.{txt, in}", "**/*constraints*.txt", "**/requirements/*.{txt,in}", "**/constraints/*.txt"], "filenames": ["constraints.txt", "requirements.in", "requirements.txt"], "id": "pip-requirements"}, {"filenames": [".condarc"], "id": "yaml"}, {"filenames": [".flake8", ".pep8", ".pyl<PERSON><PERSON>", ".pypi<PERSON>"], "id": "ini"}, {"filenames": ["Pipfile", "poetry.lock", "uv.lock"], "id": "toml"}, {"filenames": ["Pipfile.lock"], "id": "json"}], "menus": {"issue/reporter": [{"command": "python.reportIssue"}], "commandPalette": [{"category": "Python", "command": "python.analysis.restartLanguageServer", "title": "Restart Language Server", "when": "!virtualWorkspace && shellExecutionSupported && (editorLangId == python || notebookType == jupyter-notebook)"}, {"category": "Python", "command": "python.clearCacheAndReload", "title": "Clear Cache and Reload Window", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.clearWorkspaceInterpreter", "title": "Clear Workspace Interpreter Setting", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.configureTests", "title": "Configure Tests", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.createEnvironment", "title": "Create Environment...", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.createEnvironment-button", "title": "Create Environment...", "when": "false"}, {"category": "Python", "command": "python.createTerminal", "title": "Create Terminal", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.execInTerminal", "title": "Run Python File in Terminal", "when": "!virtualWorkspace && shellExecutionSupported && editorLangId == python"}, {"category": "Python", "command": "python.execInTerminal-icon", "icon": "$(play)", "title": "Run Python File", "when": "false"}, {"category": "Python", "command": "python.execInDedicatedTerminal", "icon": "$(play)", "title": "Run Python File in Dedicated Terminal", "when": "false"}, {"category": "Python", "command": "python.execSelectionInDjangoShell", "title": "Run Selection/Line in Django Shell", "when": "!virtualWorkspace && shellExecutionSupported && editorLangId == python"}, {"category": "Python", "command": "python.execSelectionInTerminal", "title": "Run Selection/Line in Python Terminal", "when": "!virtualWorkspace && shellExecutionSupported && editorLangId == python"}, {"category": "Python", "command": "python.execInREPL", "title": "Run Selection/Line in Native Python REPL", "when": "false"}, {"category": "Python", "command": "python.reportIssue", "title": "Report Issue...", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Test", "command": "testing.reRunFailTests", "icon": "$(run-errors)", "title": "<PERSON><PERSON> Failed Tests", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.setInterpreter", "title": "Select Interpreter", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.startREPL", "title": "Start Terminal REPL", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.startNativeREPL", "title": "Start Native Python REPL", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.viewLanguageServerOutput", "enablement": "python.hasLanguageServerOutputChannel", "title": "Show Language Server Output", "when": "!virtualWorkspace && shellExecutionSupported"}, {"category": "Python", "command": "python.viewOutput", "title": "Show Output", "when": "!virtualWorkspace && shellExecutionSupported"}], "editor/content": [{"group": "Python", "command": "python.createEnvironment-button", "when": "showCreateEnvButton && resourceLangId == pip-requirements && !virtualWorkspace && shellExecutionSupported && !inDiffEditor && !isMergeResultEditor && pythonDepsNotInstalled"}, {"group": "Python", "command": "python.createEnvironment-button", "when": "showCreateEnvButton && resourceFilename == pyproject.toml && pipInstallableToml && !virtualWorkspace && shellExecutionSupported  && !inDiffEditor && !isMergeResultEditor && pythonDepsNotInstalled"}], "editor/context": [{"submenu": "python.run", "group": "Python", "when": "editorLangId == python && !virtualWorkspace && shellExecutionSupported && isWorkspaceTrusted && !inChat  && notebookType != jupyter-notebook"}, {"submenu": "python.runFileInteractive", "group": "Jupyter2", "when": "editorLangId == python && !virtualWorkspace && shellExecutionSupported && !isJupyterInstalled && isWorkspaceTrusted && !inChat"}], "python.runFileInteractive": [{"command": "python.installJupyter", "group": "Jupyter2", "when": "resourceLangId == python && !virtualWorkspace && shellExecutionSupported"}], "python.run": [{"command": "python.execInTerminal", "group": "Python", "when": "resourceLangId == python && !virtualWorkspace && shellExecutionSupported"}, {"command": "python.execSelectionInDjangoShell", "group": "Python", "when": "editorHasSelection && editorLangId == python && python.isDjangoProject && !virtualWorkspace && shellExecutionSupported"}, {"command": "python.execSelectionInTerminal", "group": "Python", "when": "!config.python.REPL.sendToNativeREPL && editorFocus && editorLangId == python && !virtualWorkspace && shellExecutionSupported"}, {"command": "python.execInREPL", "group": "Python", "when": "editorFocus && editorLangId == python && !virtualWorkspace && shellExecutionSupported && config.python.REPL.sendToNativeREPL"}], "editor/title/run": [{"command": "python.execInTerminal-icon", "group": "navigation@0", "title": "Run Python File", "when": "resourceLangId == python && !isInDiffEditor && !virtualWorkspace && shellExecutionSupported"}, {"command": "python.execInDedicatedTerminal", "group": "navigation@0", "title": "Run Python File in Dedicated Terminal", "when": "resourceLangId == python && !isInDiffEditor && !virtualWorkspace && shellExecutionSupported"}], "explorer/context": [{"command": "python.execInTerminal", "group": "Python", "when": "resourceLangId == python && !virtualWorkspace && shellExecutionSupported"}], "file/newFile": [{"command": "python.createNewFile", "group": "file", "when": "!virtualWorkspace"}], "view/title": [{"command": "testing.reRunFailTests", "when": "view == workbench.view.testing && hasFailedTests && !virtualWorkspace && shellExecutionSupported", "group": "navigation@1"}]}, "submenus": [{"id": "python.run", "label": "Run Python", "icon": "$(play)"}, {"id": "python.runFileInteractive", "label": "Run in Interactive window"}], "viewsWelcome": [{"view": "testing", "contents": "Configure a test framework to see your tests here.\n[Configure Python Tests](command:python.configureTests)", "when": "!virtualWorkspace && shellExecutionSupported"}], "yamlValidation": [{"fileMatch": ".condarc", "url": "./schemas/condarc.json"}, {"fileMatch": "environment.yml", "url": "./schemas/conda-environment.json"}, {"fileMatch": "meta.yaml", "url": "./schemas/conda-meta.json"}]}, "copilot": {"tests": {"getSetupConfirmation": "python.copilotSetupTests"}}, "scripts": {"package": "gulp clean && gulp prePublishBundle && vsce package -o ms-python-insiders.vsix", "prePublish": "gulp clean && gulp prePublishNonBundle", "compile": "tsc -watch -p ./", "compileApi": "node ./node_modules/typescript/lib/tsc.js -b ./pythonExtensionApi/tsconfig.json", "compiled": "deemon npm run compile", "kill-compiled": "deemon --kill npm run compile", "checkDependencies": "gulp checkDependencies", "test": "node ./out/test/standardTest.js && node ./out/test/multiRootTest.js", "test:unittests": "mocha --config ./build/.mocha.unittests.json", "test:unittests:cover": "nyc --no-clean --nycrc-path ./build/.nycrc mocha --config ./build/.mocha.unittests.json", "test:functional": "mocha --require source-map-support/register --config ./build/.mocha.functional.json", "test:functional:perf": "node --inspect-brk ./node_modules/mocha/bin/_mocha --require source-map-support/register --config ./build/.mocha.functional.perf.json", "test:functional:memleak": "node --inspect-brk ./node_modules/mocha/bin/_mocha --require source-map-support/register --config ./build/.mocha.functional.json", "test:functional:cover": "nyc --no-clean --nycrc-path ./build/.nycrc mocha --require source-map-support/register --config ./build/.mocha.functional.json", "test:cover:report": "nyc --nycrc-path ./build/.nycrc  report --reporter=text --reporter=html --reporter=text-summary --reporter=cobertura", "testDebugger": "node ./out/test/testBootstrap.js ./out/test/debuggerTest.js", "testDebugger:cover": "nyc --no-clean --use-spawn-wrap --nycrc-path ./build/.nycrc --require source-map-support/register node ./out/test/debuggerTest.js", "testSingleWorkspace": "node ./out/test/testBootstrap.js ./out/test/standardTest.js", "testSingleWorkspace:cover": "nyc --no-clean --use-spawn-wrap --nycrc-path ./build/.nycrc --require source-map-support/register node ./out/test/standardTest.js", "preTestJediLSP": "node ./out/test/languageServers/jedi/lspSetup.js", "testJediLSP": "node ./out/test/languageServers/jedi/lspSetup.js && cross-env CODE_TESTS_WORKSPACE=src/test VSC_PYTHON_CI_TEST_GREP='Language Server:' node ./out/test/testBootstrap.js ./out/test/standardTest.js && node ./out/test/languageServers/jedi/lspTeardown.js", "testMultiWorkspace": "node ./out/test/testBootstrap.js ./out/test/multiRootTest.js", "testPerformance": "node ./out/test/testBootstrap.js ./out/test/performanceTest.js", "testSmoke": "cross-env INSTALL_JUPYTER_EXTENSION=true \"node ./out/test/smokeTest.js\"", "testInsiders": "cross-env VSC_PYTHON_CI_TEST_VSC_CHANNEL=insiders INSTALL_PYLANCE_EXTENSION=true TEST_FILES_SUFFIX=insiders.test CODE_TESTS_WORKSPACE=src/testMultiRootWkspc/smokeTests \"node ./out/test/standardTest.js\"", "lint-staged": "node gulpfile.js", "lint": "eslint  src build pythonExtensionApi", "lint-fix": "eslint --fix src build pythonExtensionApi gulpfile.js", "format-check": "prettier --check 'src/**/*.ts' 'build/**/*.js' '.github/**/*.yml' gulpfile.js", "format-fix": "prettier --write 'src/**/*.ts' 'build/**/*.js' '.github/**/*.yml' gulpfile.js", "clean": "gulp clean", "addExtensionPackDependencies": "gulp addExtensionPackDependencies", "updateBuildNumber": "gulp updateBuildNumber", "verifyBundle": "gulp verifyBundle", "webpack": "webpack"}, "dependencies": {"@iarna/toml": "^2.2.5", "@vscode/extension-telemetry": "^0.8.4", "arch": "^2.1.0", "fs-extra": "^11.2.0", "glob": "^7.2.0", "iconv-lite": "^0.6.3", "inversify": "^6.0.2", "jsonc-parser": "^3.0.0", "lodash": "^4.17.21", "minimatch": "^5.0.1", "named-js-regexp": "^1.3.3", "node-stream-zip": "^1.6.0", "reflect-metadata": "^0.2.2", "rxjs": "^6.5.4", "rxjs-compat": "^6.5.4", "semver": "^7.5.2", "stack-trace": "0.0.10", "sudo-prompt": "^9.2.1", "tmp": "^0.0.33", "uint64be": "^3.0.0", "unicode": "^14.0.0", "vscode-debugprotocol": "^1.28.0", "vscode-jsonrpc": "^9.0.0-next.5", "vscode-languageclient": "^10.0.0-next.12", "vscode-languageserver-protocol": "^3.17.6-next.10", "vscode-tas-client": "^0.1.84", "which": "^2.0.2", "winreg": "^1.2.4", "xml2js": "^0.5.0"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/bent": "^7.3.0", "@types/chai": "^4.1.2", "@types/chai-arrays": "^2.0.0", "@types/chai-as-promised": "^7.1.0", "@types/download": "^8.0.1", "@types/fs-extra": "^11.0.4", "@types/glob": "^7.2.0", "@types/lodash": "^4.14.104", "@types/mocha": "^9.1.0", "@types/node": "^22.5.0", "@types/semver": "^5.5.0", "@types/shortid": "^0.0.29", "@types/sinon": "^17.0.3", "@types/stack-trace": "0.0.29", "@types/tmp": "^0.0.33", "@types/vscode": "^1.93.0", "@types/which": "^2.0.1", "@types/winreg": "^1.2.30", "@types/xml2js": "^0.4.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vscode/test-electron": "^2.3.8", "@vscode/vsce": "^2.27.0", "bent": "^7.3.12", "chai": "^4.1.2", "chai-arrays": "^2.0.0", "chai-as-promised": "^7.1.1", "copy-webpack-plugin": "^9.1.0", "cross-env": "^7.0.3", "cross-spawn": "^6.0.5", "del": "^6.0.0", "download": "^8.0.0", "eslint": "^8.57.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-no-only-tests": "^3.3.0", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.0", "expose-loader": "^3.1.0", "flat": "^5.0.2", "get-port": "^5.1.1", "gulp": "^5.0.0", "gulp-typescript": "^5.0.0", "mocha": "^11.1.0", "mocha-junit-reporter": "^2.0.2", "mocha-multi-reporters": "^1.1.7", "node-has-native-dependencies": "^1.0.2", "node-loader": "^1.0.2", "node-polyfill-webpack-plugin": "^1.1.4", "nyc": "^15.0.0", "prettier": "^2.0.2", "rewiremock": "^3.13.0", "shortid": "^2.2.8", "sinon": "^18.0.0", "source-map-support": "^0.5.12", "ts-loader": "^9.2.8", "ts-mockito": "^2.5.0", "ts-node": "^10.7.0", "tsconfig-paths-webpack-plugin": "^3.2.0", "typemoq": "^2.1.0", "typescript": "~5.2", "uuid": "^8.3.2", "webpack": "^5.76.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.9.2", "webpack-fix-default-import-plugin": "^1.0.3", "webpack-merge": "^5.8.0", "webpack-node-externals": "^3.0.0", "webpack-require-from": "^1.8.6", "worker-loader": "^3.0.8", "yargs": "^15.3.1"}, "extensionPack": ["ms-python.vscode-pylance", "ms-python.debugpy"]}, "location": {"$mid": 1, "path": "/c:/Users/<USER>/.cursor/extensions/ms-python.python-2025.6.1-win32-x64", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "win32-x64", "publisherDisplayName": "ms-python", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1750448475945, "pinned": false, "source": "gallery", "id": "f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5", "publisherId": "998b010b-e2af-44a5-a6cd-0b5fd3b9b6f8", "publisherDisplayName": "ms-python", "targetPlatform": "win32-x64", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false, "size": 30510031}, "isValid": true, "validations": [], "preRelease": false}]}