2025-07-30 22:02:26.039 [info] [main] Log level: Info
2025-07-30 22:02:26.039 [info] [main] Validating found git in: "C:\Program Files\Git\cmd\git.exe"
2025-07-30 22:02:26.072 [info] [main] Using git "2.45.2.windows.1" from "C:\Program Files\Git\cmd\git.exe"
2025-07-30 22:02:26.072 [info] [Model][doInitialScan] Initial repository scan started
2025-07-30 22:02:26.075 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-07-30 22:02:26.930 [info] > git symbolic-ref --short refs/remotes/origin/HEAD [51ms]
2025-07-30 22:02:26.930 [info] fatal: ref refs/remotes/origin/HEAD is not a symbolic ref
2025-07-30 22:02:26.980 [info] > git rev-parse --verify origin/main [45ms]
2025-07-30 22:02:36.125 [info] > git config --global user.name [46ms]
2025-07-30 22:02:36.173 [info] > git config --global user.email [45ms]
2025-07-30 22:02:36.173 [info] [main] Stored git author name in global state: MSADDAR Youness <<EMAIL>>
